<template>
  <van-image-preview
    v-model:show="show"
    :images="imageList"
    :start-position="startPosition"
    :closeable="true"
    :show-index="true"
    :swipe-duration="300"
    @change="handleChange"
    @close="handleClose"
  >
    <template #index>
      <div class="custom-index">
        {{ currentIndex + 1 }} / {{ imageList.length }}
      </div>
    </template>
  </van-image-preview>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  images: {
    type: Array,
    default: () => []
  },
  initialIndex: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['update:visible', 'change', 'close'])

// 响应式数据
const show = ref(false)
const currentIndex = ref(0)
const startPosition = ref(0)

// 计算图片列表（只包含图片类型）
const imageList = computed(() => {
  return props.images
    .filter(item => item.type === 'image')
    .map(item => item.url)
})

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  show.value = newVal
  if (newVal) {
    // 计算在纯图片列表中的位置
    const imageItems = props.images.filter(item => item.type === 'image')
    const targetImageIndex = imageItems.findIndex((_, index) => {
      // 找到原始列表中对应的图片索引
      let imageCount = 0
      for (let i = 0; i <= props.initialIndex; i++) {
        if (props.images[i]?.type === 'image') {
          if (imageCount === index) {
            return i === props.initialIndex
          }
          imageCount++
        }
      }
      return false
    })
    
    startPosition.value = Math.max(0, targetImageIndex)
    currentIndex.value = startPosition.value
  }
})

// 监听 show 变化
watch(show, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleChange = (index) => {
  currentIndex.value = index
  emit('change', index)
}

const handleClose = () => {
  show.value = false
  emit('close')
}
</script>

<style scoped lang="less">
.custom-index {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: @radius-4;
  font-size: @font-size-14;
  z-index: 1000;
}

:deep(.van-image-preview__image) {
  background-color: #000;
}

:deep(.van-image-preview__close) {
  top: 20px;
  right: 60px;
  color: white;
  font-size: 24px;
}
</style>