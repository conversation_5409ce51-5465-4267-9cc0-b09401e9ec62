<template>
  <WoActionBar>
    <div class="action-bar">
      <!-- 操作区域 -->
      <div class="action-content">
        <div class="cart-wrapper">
          <div class="cart-icon-wrapper" @click="handleGoToCart">
            <van-badge :content="cartCount" :show-zero="false" :max="99" class="cart-badge">
              <img src="./assets/cart-icon.png" alt="购物车" class="cart-icon" />
            </van-badge>
          </div>
          <span class="cart-text">购物车</span>
        </div>

        <div class="buttons-wrapper">
          <WoButton
            type="secondary"
            size="medium"
            :disabled="cartButtonDisabled"
            @click="handleAddToCart"
          >
            加入购物车
          </WoButton>
          <WoButton
            type="gradient"
            size="medium"
            :disabled="cartButtonDisabled"
            @click="handleBuyNow"
          >
             {{ bizCode === 'zq' ? '立即提交' :'立即购买'}}
          </WoButton>
        </div>
      </div>
    </div>
  </WoActionBar>
</template>

<script setup>
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import { getBizCode } from '@utils/curEnv.js'

defineProps({
  cartCount: {
    type: Number,
    default: 0
  },
  cartButtonDisabled: {
    type: Boolean,
    default: false
  }
})

const bizCode = getBizCode()

const emit = defineEmits(['go-to-cart', 'add-to-cart', 'buy-now'])

const handleGoToCart = () => {
  emit('go-to-cart')
}

const handleAddToCart = () => {
  emit('add-to-cart')
}

const handleBuyNow = () => {
  emit('buy-now')
}
</script>

<style scoped lang="less">
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: @bg-color-white;
  padding: 0 17px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.05);

  // 操作区域
  .action-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .cart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0 0 0;
    box-sizing: border-box;

    .cart-icon-wrapper {
      position: relative;

      .cart-icon {
        width: 24px;
        height: 24px;
      }

      .cart-badge {
        :deep(.van-badge) {
          background-color: @theme-color;
          border-color: @theme-color;
        }
      }

      // 购物车数量显示
      .num {
        position: absolute;
        top: -8px;
        right: -2px;
        min-width: 16px;
        height: 16px;
        background-color: #ff4444;
        color: white;
        border-radius: 8px;
        font-size: @font-size-11;
        line-height: 16px;
        text-align: center;
        padding: 0 4px;
        box-sizing: border-box;
        z-index: 10;

        &.animation {
          animation: shake 0.6s ease-in-out;
        }
      }
    }

    .cart-text {
      font-size: @font-size-11;
      color: @text-color-secondary;
    }
  }

  .buttons-wrapper {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

// 购物车震动动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}
</style>
