<template>
  <div v-if="isDataGet" class="status-tips-overlay">
    <div class="status-tips-container">
      <div class="status-tip tips-state" v-if="!onSaleState">
        <div class="tip-icon">⚠️</div>
        <div class="tip-content">
          <div class="tip-title">商品已下架</div>
          <div class="tip-message">该商品已下架，请选购其他商品!</div>
        </div>
      </div>
      <div class="status-tip tips-stock" v-if="onSaleState && !stockState">
        <div class="tip-icon">📦</div>
        <div class="tip-content">
          <div class="tip-title">暂时无货</div>
          <div class="tip-message">所选地区暂时无货，非常抱歉！</div>
        </div>
      </div>
      <div class="status-tip tips-permission" v-if="!userStatus">
        <div class="tip-icon">🚫</div>
        <div class="tip-content">
          <div class="tip-title">无购买资格</div>
          <div class="tip-message">您暂无购买资格，非常抱歉！</div>
        </div>
      </div>
      <div class="status-tip tips-region" v-if="!regionalSalesState">
        <div class="tip-icon">📍</div>
        <div class="tip-content">
          <div class="tip-title">区域限制</div>
          <div class="tip-message">抱歉，此商品在所选区域暂不支持销售!</div>
        </div>
      </div>
      <div class="status-tip tips-limit" v-if="!limitState">
        <div class="tip-icon">🔢</div>
        <div class="tip-content">
          <div class="tip-title">限购商品</div>
          <div class="tip-message">该商品限购，请选购其他商品!</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isDataGet: {
    type: Boolean,
    default: false
  },
  onSaleState: {
    type: Boolean,
    default: true
  },
  stockState: {
    type: Boolean,
    default: true
  },
  userStatus: {
    type: Boolean,
    default: true
  },
  regionalSalesState: {
    type: Boolean,
    default: true
  },
  limitState: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped lang="less">
.status-tips-overlay {
  position: fixed;
  bottom: 49px;
  left: 0;
  right: 0;
  z-index: 999;
  pointer-events: none;

  .status-tips-container {
    pointer-events: auto;

    .status-tip {
      display: flex;
      align-items: flex-start;
      background: rgba(0, 0, 0, 0.85);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 10px;
      margin-bottom: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      animation: slideInUp 0.3s ease-out;

      .tip-icon {
        font-size: 24px;
        margin-right: 12px;
        flex-shrink: 0;
        line-height: 1;
      }

      .tip-content {
        flex: 1;

        .tip-title {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
          line-height: 1.3;
        }

        .tip-message {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
          line-height: 1.4;
        }
      }

      // 不同状态的主题色
      &.tips-state {
        border-left: 4px solid #ff6b6b;

        .tip-icon {
          filter: hue-rotate(0deg);
        }
      }

      &.tips-stock {
        border-left: 4px solid #ffa726;

        .tip-icon {
          filter: hue-rotate(30deg);
        }
      }

      &.tips-permission {
        border-left: 4px solid #ef5350;

        .tip-icon {
          filter: hue-rotate(0deg);
        }
      }

      &.tips-region {
        border-left: 4px solid #42a5f5;

        .tip-icon {
          filter: hue-rotate(200deg);
        }
      }

      &.tips-limit {
        border-left: 4px solid #ab47bc;

        .tip-icon {
          filter: hue-rotate(280deg);
        }
      }
    }
  }
}

// 滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式适配
@media (max-width: 375px) {
  .status-tips-overlay {
    .status-tips-container {
      padding: 0 5px;

      .status-tip {
        padding: 10px;

        .tip-icon {
          font-size: 20px;
          margin-right: 10px;
        }

        .tip-content {
          .tip-title {
            font-size: 15px;
          }

          .tip-message {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>