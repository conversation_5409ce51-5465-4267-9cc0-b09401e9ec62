<template>
  <div class="goods-detail-skeleton">
    <!-- 商品图片区域骨架屏 -->
    <div class="skeleton-image-section">
      <div class="skeleton-swiper">
        <div class="skeleton-image"></div>
        <div class="skeleton-dots">
          <div class="skeleton-dot" v-for="i in 3" :key="i"></div>
        </div>
      </div>
    </div>

    <!-- 商品信息区域骨架屏 -->
    <div class="skeleton-info-section">
      <!-- 价格骨架屏 -->
      <div class="skeleton-price-section">
        <div class="skeleton-price"></div>
        <div class="skeleton-original-price"></div>
      </div>

      <!-- 商品标题骨架屏 -->
      <div class="skeleton-title">
        <div class="skeleton-title-line"></div>
        <div class="skeleton-title-line short"></div>
      </div>

      <!-- 规格选择骨架屏 -->
      <div class="skeleton-spec-section">
        <div class="skeleton-spec-header">
          <div class="skeleton-spec-icon"></div>
          <div class="skeleton-spec-text"></div>
          <div class="skeleton-arrow"></div>
        </div>
        <div class="skeleton-spec-options">
          <div class="skeleton-spec-option" v-for="i in 4" :key="i"></div>
        </div>
      </div>

      <!-- 配送信息骨架屏 -->
      <div class="skeleton-delivery-section">
        <div class="skeleton-delivery-item" v-for="i in 3" :key="i">
          <div class="skeleton-delivery-icon"></div>
          <div class="skeleton-delivery-text"></div>
        </div>
      </div>
    </div>

    <!-- 商品介绍区域骨架屏 -->
    <div class="skeleton-introduce-section">
      <div class="skeleton-introduce-content">
        <div class="skeleton-introduce-line" v-for="i in 5" :key="i"></div>
      </div>
    </div>

    <!-- 底部操作栏骨架屏 -->
    <div class="skeleton-action-bar">
      <div class="skeleton-cart-section">
        <div class="skeleton-cart-icon"></div>
        <div class="skeleton-cart-text"></div>
      </div>
      <div class="skeleton-button-section">
        <div class="skeleton-button"></div>
        <div class="skeleton-button primary"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 骨架屏组件不需要任何逻辑
</script>

<style scoped lang="less">
.goods-detail-skeleton {
  min-height: 100vh;
  background-color: @bg-color-white;
  padding-bottom: 55px;
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

// 图片区域骨架屏
.skeleton-image-section {
  background-color: @bg-color-white;
  
  .skeleton-swiper {
    position: relative;
    
    .skeleton-image {
      .skeleton-base();
      width: 100%;
      height: 375px; // 与实际轮播图高度一致
      border-radius: 0;
    }
    
    .skeleton-dots {
      position: absolute;
      bottom: 12px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;
      
      .skeleton-dot {
        .skeleton-base();
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
    }
  }
}

// 商品信息区域骨架屏
.skeleton-info-section {
  background-color: @bg-color-white;
  padding: 13px 17px;
  
  .skeleton-price-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    
    .skeleton-price {
      .skeleton-base();
      width: 120px;
      height: 32px;
    }
    
    .skeleton-original-price {
      .skeleton-base();
      width: 80px;
      height: 16px;
    }
  }
  
  .skeleton-title {
    margin-bottom: 16px;
    
    .skeleton-title-line {
      .skeleton-base();
      height: 20px;
      margin-bottom: 8px;
      
      &.short {
        width: 60%;
      }
    }
  }
  
  .skeleton-spec-section {
    margin-bottom: 20px;
    
    .skeleton-spec-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      .skeleton-spec-icon {
        .skeleton-base();
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
      }
      
      .skeleton-spec-text {
        .skeleton-base();
        flex: 1;
        height: 16px;
        margin-right: 8px;
      }
      
      .skeleton-arrow {
        .skeleton-base();
        width: 8px;
        height: 8px;
      }
    }
    
    .skeleton-spec-options {
      display: flex;
      gap: 12px;
      
      .skeleton-spec-option {
        .skeleton-base();
        width: 60px;
        height: 60px;
        border-radius: 8px;
      }
    }
  }
  
  .skeleton-delivery-section {
    .skeleton-delivery-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      .skeleton-delivery-icon {
        .skeleton-base();
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
      }
      
      .skeleton-delivery-text {
        .skeleton-base();
        flex: 1;
        height: 16px;
      }
    }
  }
}

// 商品介绍区域骨架屏
.skeleton-introduce-section {
  background-color: @bg-color-white;
  margin-top: 8px;
  padding: 16px;
  
  .skeleton-introduce-content {
    .skeleton-introduce-line {
      .skeleton-base();
      height: 16px;
      margin-bottom: 12px;
      
      &:nth-child(odd) {
        width: 100%;
      }
      
      &:nth-child(even) {
        width: 80%;
      }
      
      &:last-child {
        margin-bottom: 0;
        width: 60%;
      }
    }
  }
}

// 底部操作栏骨架屏
.skeleton-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: @bg-color-white;
  padding: 10px 17px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
  
  .skeleton-cart-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    
    .skeleton-cart-icon {
      .skeleton-base();
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
    
    .skeleton-cart-text {
      .skeleton-base();
      width: 32px;
      height: 12px;
    }
  }
  
  .skeleton-button-section {
    flex: 1;
    display: flex;
    gap: 8px;
    
    .skeleton-button {
      .skeleton-base();
      flex: 1;
      height: 44px;
      border-radius: 22px;
      
      &.primary {
        background: linear-gradient(90deg, #ff9a4a 25%, #ff7a0a 50%, #ff9a4a 75%);
        background-size: 200px 100%;
        animation: skeleton-loading 1.5s infinite;
      }
    }
  }
}
</style>