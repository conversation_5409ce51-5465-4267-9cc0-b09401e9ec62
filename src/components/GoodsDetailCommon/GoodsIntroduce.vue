<template>
  <div class="goods-introduce">
    <h2 class="goods-introduce__title">商品信息</h2>
    <div ref="contentWrapperRef" class="goods-introduce__content" :style="contentWrapperStyle">
      <!-- 加载状态 -->
      <div v-if="hasHtmlContent && isCalculating" class="goods-introduce__loading">
        <div class="goods-introduce__loading-spinner"></div>
      </div>
      <!-- HTML内容展示 -->
      <div v-if="hasHtmlContent" v-html="htmlContent" ref="htmlContentRef" class="goods-introduce__html-content"
        :style="scaleStyle" />
      <!-- 图片列表展示 -->
      <div v-else class="goods-introduce__image-list">
        <img v-for="(imageUrl, index) in imageUrls" :key="index" :src="imageUrl" :alt="`商品介绍图片${index + 1}`"
          loading="lazy" decoding="async" class="goods-introduce__image" @error="handleImageError" />
      </div>
    </div>
    <img src="./assets/businessLicense.png" alt="营业执照" class="goods-introduce__license" loading="lazy"
         decoding="async" />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, toRefs, watchEffect } from 'vue'
import { debounce } from 'lodash-es'

const props = defineProps({
  currentSKU: {
    type: Object,
    default: () => ({})
  }
})

const { currentSKU } = toRefs(props)

const contentWrapperRef = ref(null)
const htmlContentRef = ref(null)

const scale = ref(1)
const contentHeight = ref('auto')
const isCalculating = ref(false)

const hasHtmlContent = computed(() => Boolean(currentSKU.value?.introduction))

const imageUrls = computed(() => {
  const introduceList = currentSKU.value?.introduceList
  if (!introduceList) return []

  const urlString = typeof introduceList === 'string'
    ? introduceList
    : String(introduceList)

  return urlString.split(',').filter(Boolean)
})

const htmlContent = computed(() => currentSKU.value?.introduction || '')

const scaleStyle = computed(() => ({
  transform: `scale(${scale.value})`,
  transformOrigin: '0 0',
  opacity: isCalculating.value ? 0 : 1,
  transition: isCalculating.value ? 'none' : 'opacity 0.2s ease-in-out'
}))

const contentWrapperStyle = computed(() => ({
  height: contentHeight.value
}))

const removeInlineStyles = (element) => {
  if (!element?.childNodes) return

  for (const child of element.childNodes) {
    if (child.nodeType === 1) {
      if (child.tagName === 'IMG' || child.tagName === 'TABLE') {
        child.setAttribute('width', '100%')
        child.setAttribute('height', 'auto')
      } else {
        child.style.width = ''
      }
      removeInlineStyles(child)
    }
  }
}

const processHtmlContent = async () => {
  isCalculating.value = true
  await nextTick()

  const calculateDimensions = () => {
    const $el = htmlContentRef.value?.querySelector('.ssd-module-wrap') ||
      htmlContentRef.value?.querySelector('.ssd-module-mobile-wrap') ||
      htmlContentRef.value?.querySelector('.wrapper-jd-inner') ||
      htmlContentRef.value?.querySelector('.wrapper-jd-inner')?.firstElementChild ||
      htmlContentRef.value?.firstElementChild ||
      htmlContentRef.value

    if (!$el) {
      console.warn('未找到目标元素')
      return false
    }

    removeInlineStyles($el)

    const contentComputedStyle = window.getComputedStyle($el)
    const realWidth = parseInt(contentComputedStyle.width)
    const realHeight = parseInt(contentComputedStyle.height)

    // 检查宽度和高度是否有效
    if (!realWidth || !realHeight || realWidth <= 0 || realHeight <= 0) {
      console.warn('元素尺寸无效:', { realWidth, realHeight, display: contentComputedStyle.display, visibility: contentComputedStyle.visibility })
      return false
    }

    const clientWidth = document.documentElement.clientWidth
    const isLargeScreen = clientWidth > 540

    if (isLargeScreen) {
      // 大屏模式下使用全宽
      const newScale = clientWidth / realWidth
      const scaleHeight = newScale * realHeight

      scale.value = newScale
      contentHeight.value = `${scaleHeight}px`
    } else {
      // 小屏模式保持原有逻辑
      const newScale = clientWidth / realWidth
      const scaleHeight = newScale * realHeight

      scale.value = newScale
      contentHeight.value = `${scaleHeight}px`
    }

    console.log('尺寸计算完成:', { realWidth, realHeight, scale: scale.value, contentHeight: contentHeight.value })
    isCalculating.value = false
    return true
  }

  // 首次尝试
  setTimeout(() => {
    if (!calculateDimensions()) {
      // 如果首次失败，等待更长时间后重试
      setTimeout(() => {
        if (!calculateDimensions()) {
          // 最后一次尝试，等待图片加载
          const images = htmlContentRef.value?.querySelectorAll('img') || []
          if (images.length > 0) {
            let loadedCount = 0
            const totalImages = images.length

            const checkAllLoaded = () => {
              loadedCount++
              if (loadedCount === totalImages) {
                setTimeout(() => {
                  if (!calculateDimensions()) {
                    isCalculating.value = false
                  }
                }, 50)
              }
            }

            images.forEach(img => {
              if (img.complete) {
                checkAllLoaded()
              } else {
                img.addEventListener('load', checkAllLoaded, { once: true })
                img.addEventListener('error', checkAllLoaded, { once: true })
              }
            })
          } else {
            // 没有图片，直接重试
            setTimeout(() => {
              if (!calculateDimensions()) {
                isCalculating.value = false
              }
            }, 200)
          }
        } else {
          isCalculating.value = false
        }
      }, 300)
    } else {
      isCalculating.value = false
    }
  }, 100)
}

const handleResize = debounce(() => {
  if (hasHtmlContent.value) {
    processHtmlContent()
  }
}, 150)

const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  event.target.style.display = 'none'
}

// 使用防抖处理内容变化
const debouncedProcessHtml = debounce(processHtmlContent, 100)

watchEffect(() => {
  if (hasHtmlContent.value && htmlContent.value) {
    nextTick(() => {
      debouncedProcessHtml()
    })
  }
})

watch(
  () => currentSKU.value?.introduction,
  (newIntroduction) => {
    if (newIntroduction) {
      // 重置状态
      scale.value = 1
      contentHeight.value = 'auto'
      isCalculating.value = true
      nextTick(() => {
        debouncedProcessHtml()
      })
    }
  }
)

onMounted(() => {
  if (hasHtmlContent.value) {
    processHtmlContent()
  }
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  handleResize.cancel()
  debouncedProcessHtml.cancel()
})
</script>

<style scoped lang="less">
.goods-introduce {
  width: 100%;
  font-size: 16px;
  contain: layout style paint;



  &__title {
    height: 43px;
    line-height: 43px;
    font-size: 15px;
    color: #171e24;
    text-align: center;
    font-weight: 500;
    margin: 0;
  }

  &__content {
    position: relative;
    overflow: hidden;
  }

  &__html-content {
    will-change: transform;

    :deep(img),
    :deep(table) {
      width: 100% !important;
      height: auto !important;
      max-width: 100%;
    }

    :deep(br) {
      display: none;
    }
  }

  &__image-list {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  &__image {
    display: block;
    width: 100%;
    height: auto;
    object-fit: cover;
  }

  &__license {
    display: block;
    width: 100%;
    max-width: 400px;
    height: auto;
    margin: 20px auto 0;
    object-fit: contain;
  }

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }

  &__loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
