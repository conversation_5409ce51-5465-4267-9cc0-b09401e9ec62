<template>
  <section class="marketing-section" v-if="marketTemplates && marketTemplates.length > 0 && marketTemplates[0].upImage">
    <div class="goods-marketing">
      <img @click="handleMarketingClick" :src="marketTemplates[0].upImage" alt="营销活动" />
    </div>
  </section>
</template>

<script setup>
defineProps({
  marketTemplates: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['marketing-click'])

const handleMarketingClick = () => {
  emit('marketing-click')
}
</script>

<style scoped lang="less">
.marketing-section {
  background-color: @bg-color-white;
  padding: 5px 0;
  box-sizing: border-box;
}

.goods-marketing {
  img {
    width: 100vw;
    height: auto;
    display: block;
    cursor: pointer;
  }
}
</style>