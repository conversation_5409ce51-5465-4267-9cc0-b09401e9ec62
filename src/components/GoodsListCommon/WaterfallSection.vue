<template>
  <van-list :loading="loadMode === 'scroll' ? waterfallLoading : false"
    :finished="loadMode === 'scroll' ? waterfallFinished : true" :immediate-check="loadMode === 'scroll'"
    @load="handleScrollLoad">
    <!-- 顶部插槽 -->
    <slot name="header"></slot>

    <div class="home-waterfall-container">
      <!-- 骨架屏插槽 -->
      <slot name="skeleton" :loading="skeletonStates.waterfall && waterfallGoodsList.length === 0">
        <WaterfallSkeleton v-if="skeletonStates.waterfall && waterfallGoodsList.length === 0" :skeleton-count="6" />
      </slot>
      <Waterfall v-show="waterfallGoodsList.length > 0" ref="waterfallRef" :list="waterfallGoodsList"
        :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0" :animationDelay="0"
        :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true"
        @afterRender="handleWaterfallAfterRender">
        <template #default="{ item }">
          <slot name="item" :item="item" :handleGoodsClick="handleGoodsClick" :handleImageLoaded="handleImageLoaded">
            <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)"
              @image-loaded="handleImageLoaded" />
          </slot>
        </template>
      </Waterfall>
    </div>

    <!-- 加载更多按钮插槽 -->
    <slot name="load-more"
      :show="loadMode === 'button' && waterfallGoodsList.length > 0 && !waterfallFinished && waterfallButtonCanShow && waterfallRenderComplete && !skeletonStates.waterfall"
      :loading="waterfallLoading" :handleLoadMore="handleWaterfallLoadMore">
      <div class="home-load-more-container"
        v-if="loadMode === 'button' && waterfallGoodsList.length > 0 && !waterfallFinished && waterfallButtonCanShow && waterfallRenderComplete && !skeletonStates.waterfall">
        <WoButton type="text" :disabled="waterfallLoading" @click="handleWaterfallLoadMore"
          class="home-load-more-button">
          {{ waterfallLoading ? '加载中...' : '点击加载更多' }}
        </WoButton>
      </div>
    </slot>

    <!-- 无更多数据插槽 -->
    <slot name="no-more" :show="waterfallGoodsList.length > 0 && waterfallFinished && !skeletonStates.waterfall">
      <div class="home-no-more-text"
        v-if="waterfallGoodsList.length > 0 && waterfallFinished && !skeletonStates.waterfall">
        <span>没有更多了</span>
      </div>
    </slot>

    <!-- 空状态插槽 -->
    <slot name="empty" :show="waterfallGoodsList.length === 0 && !skeletonStates.waterfall && waterfallRenderComplete">
      <div class="home-empty-state"
        v-if="waterfallGoodsList.length === 0 && !skeletonStates.waterfall && waterfallRenderComplete">
        <span>暂无商品</span>
      </div>
    </slot>

    <!-- 底部插槽 -->
    <slot name="footer"></slot>
  </van-list>
</template>

<script setup>
import { ref, toRefs, watch, nextTick } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import WaterfallSkeleton from '@views/Home/components/Skeleton/WaterfallSkeleton.vue'
import ProductCard from '@views/Home/components/ProductCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'

const props = defineProps({
  waterfallGoodsList: {
    type: Array,
    default: () => []
  },
  waterfallLoading: {
    type: Boolean,
    default: false
  },
  waterfallFinished: {
    type: Boolean,
    default: false
  },
  waterfallButtonCanShow: {
    type: Boolean,
    default: false
  },
  waterfallRenderComplete: {
    type: Boolean,
    default: false
  },
  skeletonStates: {
    type: Object,
    default: () => ({
      waterfall: true
    })
  },
  loadMode: {
    type: String,
    default: 'button', // 'button' | 'scroll'
    validator: (value) => ['button', 'scroll'].includes(value)
  }
})

const emit = defineEmits(['goods-click', 'load-more', 'after-render'])

const {
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  skeletonStates,
  loadMode
} = toRefs(props)

const waterfallRef = ref(null)
const breakpoints = ref(getDefaultBreakpoints())


// 渲染调度与渲染锁，避免快速追加时布局错位
const isRendering = ref(false)
const isRelayoutPending = ref(false)

const scheduleRelayout = () => {
  if (!waterfallRef.value) return
  if (isRendering.value) {
    // 等待当前渲染完成后再补一次
    isRelayoutPending.value = true
    return
  }
  isRendering.value = true
  nextTick(() => {
    // 双重 requestAnimationFrame，确保 DOM 和图片尺寸已更新
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        waterfallRef.value && waterfallRef.value.renderer()
      })
    })
  })
}

// 当列表长度增加时，触发一次重新布局
watch(
  () => waterfallGoodsList.value.length,
  (newLen, oldLen) => {
    if (newLen > oldLen) {
      scheduleRelayout()
    }
  }
)

const handleGoodsClick = (goodsInfo) => {
  emit('goods-click', goodsInfo)
}

const handleWaterfallLoadMore = () => {
  emit('load-more')
}

const handleWaterfallAfterRender = () => {
  // 渲染完成，释放渲染锁，如有挂起的重排请求则继续
  if (isRendering.value) {
    isRendering.value = false
    if (isRelayoutPending.value) {
      isRelayoutPending.value = false
      scheduleRelayout()
    }
  }
  emit('after-render')
}

const handleScrollLoad = () => {
  // 渲染中先不继续触发加载，避免数据暴增导致布局错位
  if (isRendering.value) return
  if (loadMode.value === 'scroll' && !waterfallLoading.value && !waterfallFinished.value) {
    emit('load-more')
  }
}

const handleImageLoaded = () => {
  // 图片加载会改变高度，调度一次重排
  scheduleRelayout()
}
</script>

<style scoped lang="less">
.home-waterfall-container {
  position: relative;
  padding: 0 10px;
  box-sizing: border-box;

  :deep(.vue-waterfall) {
    opacity: 1;
  }
}

.waterfall-fade-enter-active,
.waterfall-fade-leave-active {
  transition: opacity 0.3s ease;
}

.waterfall-fade-enter-from,
.waterfall-fade-leave-to {
  opacity: 0;
}

.waterfall-fade-enter-to,
.waterfall-fade-leave-from {
  opacity: 1;
}



.home-load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;
  box-sizing: border-box;

  .home-load-more-button {
    min-width: 120px;
    height: 20px;
    font-size: @font-size-14;
    box-sizing: border-box;

    &.wo-button-text {
      background-color: transparent;
      border-radius: @radius-18;

      &:active {
        opacity: @opacity-07;
      }

      &.wo-button-disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:active {
          transform: none;
        }
      }
    }
  }
}

.home-no-more-text {
  padding: 20px 0 16px;
  text-align: center;
  box-sizing: border-box;

  span {
    font-size: @font-size-14;
    color: @text-color-tertiary;
  }
}

.home-empty-state {
  padding: 60px 0;
  text-align: center;
  box-sizing: border-box;

  span {
    font-size: @font-size-16;
    color: @text-color-secondary;
  }
}
</style>
