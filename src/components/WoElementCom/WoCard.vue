<template>
  <div class="card-section">
    <div v-if="title" class="card-title">
      {{ title }}
    </div>
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="less">
.card-section {
  background-color: @bg-color-white;
  padding: 13px 15px;
  margin-bottom: 10px;
  border-radius: 10px;
}

.card-title {
  font-size: @font-size-15;
  font-weight: @font-weight-600;
  color: @text-color-primary;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid @divider-color-base;
}

.card-content {
  width: 100%;
}
</style>
