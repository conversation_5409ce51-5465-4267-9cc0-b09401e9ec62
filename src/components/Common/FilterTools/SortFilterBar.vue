<template>
  <div class="sort-filter-bar">
    <div class="sort-item" :class="{ active: sortType === 'sort' }"
         @click="changeSortType('sort')">
      综合
    </div>
    <div class="sort-item" :class="{ active: sortType === 'price' }" @click="changeSortType('price')">
      价格
      <img
        v-if="sortType === 'price'"
        :src="sortSelectImg"
        :style="{ transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'rotate(0deg)' }"
        alt="排序"
        class="sort-icon"
      />
      <img
        v-else
        :src="sortNoSelectImg"
        alt="排序"
        class="sort-icon"
      />
    </div>
    <div class="sort-item" :class="{ active: sortType === 'sale' }" @click="changeSortType('sale')">
      销量
      <img
        v-if="sortType === 'sale'"
        :src="sortSelectImg"
        :style="{ transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'rotate(0deg)' }"
        alt="排序"
        class="sort-icon"
      />
      <img
        v-else
        :src="sortNoSelectImg"
        alt="排序"
        class="sort-icon"
      />
    </div>
    <div class="filter-item" :class="{ active: hasFilterConditions }" @click="toggleFilter">
      筛选
      <img
        :src="hasFilterConditions ? filterSelectImg : filterNoSelectImg"
        alt="筛选"
        class="filter-icon"
      />
    </div>
  </div>
</template>

<script setup>
import sortNoSelectImg from '@/static/images/sort-noselect.png'
import sortSelectImg from '@/static/images/sort-select.png'
import filterNoSelectImg from '@/static/images/filter-noselect.png'
import filterSelectImg from '@/static/images/filter-select.png'

// Props
const props = defineProps({
  sortType: {
    type: String,
    default: 'sort'
  },
  sortOrder: {
    type: String,
    default: ''
  },
  hasFilterConditions: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['sort-change', 'filter-toggle'])

// 修改排序类型切换逻辑
const changeSortType = (type) => {
  emit('sort-change', { type, currentSortType: props.sortType, currentSortOrder: props.sortOrder })
}

// 切换筛选面板显示状态
const toggleFilter = () => {
  emit('filter-toggle')
}
</script>

<style scoped lang="less">
.sort-filter-bar {
  display: flex;
  align-items: center;
  height: 35px;
  background-color: @bg-color-white;

  .sort-item,
  .filter-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: @font-size-14;
    color: @text-color-secondary;
    position: relative;

    &.active {
      color: @theme-color;
      font-weight: @font-weight-500;
    }

    .sort-icon {
      width: 8px;
      height: 12px;
      margin-left: 2px;
    }

    .filter-icon {
      width: 12px;
      height: 12px;
      margin-left: 2px;
    }
  }
}
</style>
