<template>
  <div class="address-form-component">
    <!-- 地址表单 -->
    <div class="form-section">
      <WoFormItem
        label="收货人"
        v-model="formData.recName"
        placeholder="请填写收货人姓名"
        :error="nameError"
        :error-message="nameErrorMessage"
        clearable
        @blur="validateName"
        @input="clearNameError"
      />
      <WoFormItem
        label="手机号"
        v-model="formData.recPhone"
        maxlength="11"
        placeholder="请填写11位手机号"
        type="tel"
        :error="phoneError"
        :error-message="phoneErrorMessage"
        clearable
        @blur="validatePhone"
        @input="clearPhoneError"
      />
      <WoFormItem label="所在地区" @click="onSelectRegion" clickable>
        <template #input>
          <div class="address-form__region-value"
            :class="{ 'address-form__region-value--placeholder': !formData.region }">
            {{ formData.region || '请选择省市区' }}
          </div>
        </template>
        <template #rightIcon>
          <div class="address-form__arrow-icon" @click="onSelectRegion"></div>
        </template>
      </WoFormItem>
      <WoFormItem
        label="详细地址"
        rows="2"
        type="textarea"
        autosize
        v-model="formData.addrDetail"
        placeholder="请填写详细地址，如街道、门牌号等"
        :error="addressError"
        :error-message="addressErrorMessage"
        clearable
        @blur="validateAddress"
        @input="clearAddressError"
      />
    </div>

    <!-- 地区选择弹窗 -->
    <van-popup v-model:show="showCascader" round position="bottom" :lazy-render="false">
      <van-cascader
        class="address-form__cascader"
        v-model="cascaderValue"
        title="所选地区"
        :options="cascaderOptions"
        :field-names="cascaderFieldNames"
        @close="showCascader = false"
        @change="onCascaderChange"
        @finish="onCascaderFinish"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, ref, computed, watch, onMounted } from 'vue'
import { debounce } from 'lodash-es'
import WoFormItem from '@components/WoElementCom/WoFormItem.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { queryAddrArea } from '@api/interface/address.js'

// Props
const props = defineProps({
  // 初始地址数据
  initialData: {
    type: Object,
    default: () => ({})
  },
  // 是否禁用表单
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'region-change'])

// 表单数据
const formData = reactive({
  recName: '',    // 收货人姓名
  recPhone: '',   // 收货人手机号
  region: '',     // 地区文本描述
  addrDetail: '', // 详细地址
  areaId: '',     // 最后一级地区ID
  areaType: ''    // 最后一级地区类型
})

// 级联选择器相关数据
const showCascader = ref(false)         // 控制弹层显示状态
const cascaderValue = ref('')           // 当前选中的值 (单值模式)
const cascaderValueDetails = ref([])    // 当前选中的完整对象数组 (多级路径)
const cascaderOptions = ref([])         // 级联数据源

// 字段映射配置
const cascaderFieldNames = {
  text: 'areaName',     // 显示文本对应的字段名
  value: 'areaId',      // 值对应的字段名
  children: 'children'  // 子选项对应的字段名
}

// 表单验证状态
const nameError = ref(false)
const nameErrorMessage = ref('')
const phoneError = ref(false)
const phoneErrorMessage = ref('')
const addressError = ref(false)
const addressErrorMessage = ref('')

// 计算属性
const phoneRegex = /^1\d{10}$/

// 表单整体验证状态
const isFormValid = computed(() => {
  return formData.recName.trim() &&
         phoneRegex.test(formData.recPhone.trim()) &&
         formData.region &&
         formData.addrDetail.trim().length >= 5
})

// 表单验证逻辑
const validateName = () => {
  const name = formData.recName.trim()
  if (!name) {
    nameError.value = true
    nameErrorMessage.value = '请填写收货人姓名'
    return false
  }
  if (name.length < 2) {
    nameError.value = true
    nameErrorMessage.value = '姓名至少需要2个字符'
    return false
  }
  nameError.value = false
  nameErrorMessage.value = ''
  return true
}

const validatePhone = () => {
  const phone = formData.recPhone.trim()
  if (!phone) {
    phoneError.value = true
    phoneErrorMessage.value = '请填写手机号'
    return false
  }
  if (!phoneRegex.test(phone)) {
    phoneError.value = true
    phoneErrorMessage.value = '请填写正确的11位手机号'
    return false
  }
  phoneError.value = false
  phoneErrorMessage.value = ''
  return true
}

const validateAddress = () => {
  const detail = formData.addrDetail.trim()
  if (!detail) {
    addressError.value = true
    addressErrorMessage.value = '请填写详细地址'
    return false
  }
  if (detail.length < 5) {
    addressError.value = true
    addressErrorMessage.value = '详细地址至少需要5个字符'
    return false
  }
  addressError.value = false
  addressErrorMessage.value = ''
  return true
}

// 清除错误状态的函数
const clearNameError = () => {
  if (nameError.value) {
    nameError.value = false
    nameErrorMessage.value = ''
  }
}

const clearPhoneError = () => {
  if (phoneError.value) {
    phoneError.value = false
    phoneErrorMessage.value = ''
  }
}

const clearAddressError = () => {
  if (addressError.value) {
    addressError.value = false
    addressErrorMessage.value = ''
  }
}

// 构建地址数组 - 将地址对象转换为级联选择器需要的数据格式
const buildAddressArray = (address) => {
  const levels = [
    { id: address.provinceId, name: address.provinceName, type: '1' },
    { id: address.cityId, name: address.cityName, type: '2' },
    { id: address.countyId, name: address.countyName, type: '3' },
    { id: address.townId, name: address.townName, type: '4' }
  ]

  return levels
    .filter(level => level.name)
    .map(level => ({
      areaId: level.id,
      areaName: level.name,
      areaType: level.type
    }))
}

// 填充地址表单数据
const fillAddressForm = (address) => {
  // 填充基础表单数据
  Object.assign(formData, {
    recName: address.recName || '',
    recPhone: address.recPhone || '',
    region: [address.provinceName, address.cityName, address.countyName, address.townName]
      .filter(Boolean).join('/') || '',
    addrDetail: address.addrDetail || '',
    areaId: address.areaId || '',
    areaType: address.areaType || ''
  })

  // 构建级联选择器需要的地址数组
  const addrArr = buildAddressArray(address)
  cascaderValueDetails.value = addrArr

  // 设置当前选中值
  if (addrArr.length > 0) {
    cascaderValue.value = addrArr[addrArr.length - 1].areaId
  }
}

// 查询已选择地址的详细信息，用于编辑模式下加载完整的地址层级数据
const querySelectedAddrInfo = async () => {
  // 不存在已选中数据，流程结束
  if (cascaderValueDetails.value.length === 0) return

  // 加载指定层级的地址数据
  const loadAddressLevel = async (index, parentObj) => {
    const current = cascaderValueDetails.value[index]
    if (!current) return null

    // 构建查询参数
    const area = `{"areaId":"${current.areaId}","areaType":"${current.areaType}"}`
    const [err, json] = await queryAddrArea(area)
    if (err || json.length === 0) return null

    // 第一级（省）特殊处理
    if (index === 0) {
      const obj = cascaderOptions.value.find(item => item.areaId === current.areaId)
      if (obj) {
        obj.children = json
        return obj
      }
      return null
    }

    // 其他级别，从父对象的children中查找
    if (parentObj && parentObj.children) {
      const obj = parentObj.children.find(item => item.areaId === current.areaId)
      if (obj) {
        obj.children = json
        return obj
      }
    }
    return null
  }

  // 依次加载省市区街道数据
  const provinceObj = await loadAddressLevel(0)
  if (!provinceObj) return

  const cityObj = await loadAddressLevel(1, provinceObj)
  if (!cityObj) return

  // 加载区县数据
  await loadAddressLevel(2, cityObj)
  // 街道级别不需要继续加载子级
}

// 选择地区按钮点击处理
const onSelectRegion = () => {
  if (props.disabled) return
  showCascader.value = true
}

// 级联选择器选项变化处理
const onCascaderChange = debounce(async ({ selectedOptions, tabIndex }) => {
  const selected = selectedOptions[tabIndex]

  // 如果当前节点已经加载过子节点，则不需要再次请求
  if (selected.children && selected.children.length > 0) {
    return
  }

  // 构建查询参数
  const area = JSON.stringify({
    areaId: selected.areaId,
    areaType: selected.areaType
  })

  try {
    showLoadingToast()
    const [err, json] = await queryAddrArea(area)
    closeToast()

    if (err) {
      showToast(err.msg || '查询失败')
      return
    }

    // 如果没有下一级数据，说明当前就是最后一级，直接完成选择
    if (!json || json.length === 0) {
      // 标记为叶子节点
      selected.children = null
      // 更新级联选项（通过创建新引用触发响应式更新）
      cascaderOptions.value = [...cascaderOptions.value]
      // 直接完成选择
      onCascaderFinish(selectedOptions)
      return
    }

    const updateNodeChildren = (options, path, newChildren) => {
      let current = options
      for (let i = 0; i < path.length - 1; i++) {
        const node = current.find(item => item.areaId === path[i].areaId)
        if (node && node.children) {
          current = node.children
        } else {
          return false
        }
      }

      const targetNode = current.find(item => item.areaId === path[path.length - 1].areaId)
      if (targetNode) {
        targetNode.children = newChildren
        return true
      }
      return false
    }

    const childrenData = json.map(item => ({ ...item, children: [] }))
    const success = updateNodeChildren(cascaderOptions.value, selectedOptions.slice(0, tabIndex + 1), childrenData)

    if (success) {
      // 触发响应式更新
      cascaderOptions.value = [...cascaderOptions.value]
    }
  } catch (err) {
    console.error('查询下一级数据失败:', err)
    closeToast()
    showToast(err.message || err.msg || '查询失败')
  }
}, 300)

// 级联选择器完成选择处理
const onCascaderFinish = (selectedOptions) => {
  showCascader.value = false
  cascaderValueDetails.value = selectedOptions

  // 拼接选中的地址文本
  if (selectedOptions && selectedOptions.length > 0) {
    formData.region = selectedOptions.map(option => option.areaName).join('/')

    // 存储地址ID信息，可用于提交表单
    const lastOption = selectedOptions[selectedOptions.length - 1]
    formData.areaId = lastOption.areaId
    formData.areaType = lastOption.areaType

    // 触发地区变化事件
    emit('region-change', {
      region: formData.region,
      areaId: formData.areaId,
      areaType: formData.areaType,
      cascaderValueDetails: cascaderValueDetails.value
    })
  }
}

// 表单验证函数
const validateForm = () => {
  const isNameValid = validateName()
  const isPhoneValid = validatePhone()
  const isAddressValid = validateAddress()

  if (!formData.region) {
    showToast('请选择所在地区')
    return false
  }

  const isValid = isNameValid && isPhoneValid && isAddressValid
  emit('validate', { isValid, formData })
  return isValid
}

// 构建请求参数
const buildRequestParams = () => {
  const addr = cascaderValueDetails.value
  return {
    recName: formData.recName.trim(),
    recPhone: formData.recPhone.trim(),
    addrDetail: formData.addrDetail.trim(),
    provinceId: addr[0]?.areaId || '',
    provinceName: addr[0]?.areaName || '',
    cityId: addr[1]?.areaId || '',
    cityName: addr[1]?.areaName || '',
    countyId: addr[2]?.areaId || '',
    countyName: addr[2]?.areaName || '',
    townId: addr[3]?.areaId || '',
    townName: addr[3]?.areaName || ''
  }
}

// 初始化省级数据
const initProvinceData = async () => {
  const [err, data] = await queryAddrArea()
  if (err) {
    showToast(err.msg)
    return false
  }

  cascaderOptions.value = data.map(item => ({ ...item, children: [] }))
  return true
}

// 监听表单数据变化，向父组件发送更新
watch(formData, (newData) => {
  emit('update:modelValue', { ...newData })
}, { deep: true })

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    fillAddressForm(newData)
  }
}, { immediate: true, deep: true })

// 暴露给父组件的方法
defineExpose({
  validateForm,
  buildRequestParams,
  fillAddressForm,
  isFormValid,
  formData,
  cascaderValueDetails
})

onMounted(async () => {
  try {
    // 初始化省级数据
    await initProvinceData()

    // 如果有初始数据，填充表单并查询地址信息
    if (props.initialData && Object.keys(props.initialData).length > 0) {
      fillAddressForm(props.initialData)
      await querySelectedAddrInfo()
    }
  } catch (error) {
    console.error('地址表单初始化失败:', error)
    showToast('地址表单初始化失败')
  }
})
</script>

<style scoped lang="less">
.address-form-component {
  .form-section {
    // 表单项间距优化
    :deep(.wo-form-item) {
      margin-bottom: @margin-4;

      &:last-child {
        margin-bottom: 0;
      }

      .van-field {
        padding: @padding-16 0;
        border-bottom: 1px solid @divider-color-base;
        transition: border-color @transition-base ease;

        &:focus-within {
          border-bottom-color: @theme-color;
        }

        &:last-child {
          border-bottom: none;
        }
      }

      // 标签样式优化
      .van-field__label {
        font-size: @font-size-15;
        color: @text-color-primary;
        font-weight: @font-weight-500;
        min-width: 70px;
        position: relative;

        // 必填标识
        &::after {
          content: '*';
          color: @color-red;
          margin-left: @margin-2;
          font-weight: @font-weight-400;
        }
      }

      // 输入框样式优化
      .van-field__control {
        font-size: @font-size-15;
        color: @text-color-primary;

        &::placeholder {
          color: @text-color-disabled;
          font-size: @font-size-14;
        }

        &:focus {
          outline: none;
        }
      }
    }
  }

  .address-form__region-value {
    flex: 1;
    font-size: @font-size-15;
    color: @text-color-primary;
    line-height: 1.5;
    padding: @padding-2 0;

    &--placeholder {
      color: @text-color-disabled;
      font-size: @font-size-14;
    }
  }

  .address-form__arrow-icon {
    width: 8px;
    height: 12px;
    margin-left: 8px;
    background: url('@/static/images/arrow-right-black.png') no-repeat center;
    background-size: contain;
    cursor: pointer;
    opacity: @opacity-60;
    transition: opacity @transition-base ease;

    &:hover {
      opacity: @opacity-100;
    }
  }

  .address-form__cascader {
    :deep(.van-tabs__line) {
      background: @gradient-orange-106;
      height: 3px;
      border-radius: 2px;
    }

    :deep(.van-cascader__option.van-cascader__option--selected) {
      color: @theme-color;
      font-weight: @font-weight-500;
      background: rgba(255, 122, 10, 0.08);
    }

    :deep(.van-cascader__header) {
      background: @bg-color-white;
      //border-bottom: 1px solid @divider-color-base;
    }

    :deep(.van-cascader__close-icon) {
      color: @text-color-tertiary;
    }
  }
}
</style>
