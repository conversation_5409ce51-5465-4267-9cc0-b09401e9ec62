// Tab configurations for different business codes
import homeImg from './assets/tab-home.png'
import homeActiveImg from './assets/tab-home-active.png'
import jdHomeActiveImg from './assets/jd-tab-home-active.png'
import categoryImg from './assets/tab-category.png'
import categoryActiveImg from './assets/tab-category-active.png'
import jdCategoryActiveImg from './assets/jd-tab-category-active.png'
import userImg from './assets/tab-user.png'
import userActiveImg from './assets/tab-user-active.png'
import jdUserActiveImg from './assets/jd-tab-user-active.png'
import cartImg from './assets/tab-cart.png'
import cartActiveImg from './assets/tab-cart-active.png'
import jdCartActiveImg from './assets/jd-tab-cart-active.png'
import lnzxHomeImg from './assets/lnzx-home.png'

// Default tab configuration
const defaultTabs = [
  {
    title: '首页',
    path: '/home',
    img: homeImg,
    activeImg: homeActiveImg
  },
  {
    title: '分类',
    path: '/category',
    img: categoryImg,
    activeImg: categoryActiveImg
  },
  {
    title: '购物车',
    path: '/cart',
    img: cartImg,
    activeImg: cartActiveImg
  },
  {
    title: '我的',
    path: '/user',
    img: userImg,
    activeImg: userActiveImg
  }
]

// Business-specific tab configurations
const tabConfigs = {
  ygjd: [
    {
      title: '首页',
      path: '/home',
      img: homeImg,
      activeImg: jdHomeActiveImg
    },
    {
      title: '分类',
      path: '/jd/category',
      img: categoryImg,
      activeImg: jdCategoryActiveImg
    },
    {
      title: '购物车',
      path: '/jd/cart',
      img: cartImg,
      activeImg: jdCartActiveImg
    },
    {
      title: '我的',
      path: '/jd/user',
      img: userImg,
      activeImg: jdUserActiveImg
    }
  ],

  lnzx: [
    {
      title: '',
      path: '/home',
      img: lnzxHomeImg,
      activeImg: lnzxHomeImg
    },
    {
      title: '购物车',
      path: '/cart',
      img: cartImg,
      activeImg: cartActiveImg
    },
    {
      title: '我的',
      path: '/user',
      img: userImg,
      activeImg: userActiveImg
    }
  ],

  zq: [
    {
      title: '首页',
      path: '/home',
      img: homeImg,
      activeImg: homeActiveImg
    },
    {
      title: '分类',
      path: '/category',
      img: categoryImg,
      activeImg: categoryActiveImg
    },
    {
      title: '购物车',
      path: '/cart',
      img: cartImg,
      activeImg: cartActiveImg
    },
    {
      title: '我的',
      path: '/user',
      img: userImg,
      activeImg: userActiveImg
    }
  ]
}

// Get tab configuration by business code
export const getTabConfig = (bizCode) => {
  return tabConfigs[bizCode] || defaultTabs
}

// Get all images for preloading
export const getAllImages = () => {
  return [
    homeImg, homeActiveImg, jdHomeActiveImg,
    categoryImg, categoryActiveImg, jdCategoryActiveImg,
    userImg, userActiveImg, jdUserActiveImg,
    cartImg, cartActiveImg, jdCartActiveImg,
    lnzxHomeImg
  ]
}
