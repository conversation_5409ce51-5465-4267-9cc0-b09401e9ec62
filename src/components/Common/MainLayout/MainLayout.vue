<template>
  <div class="main-layout">
    <div class="main-layout__content" :class="contentClass">
      <slot />
    </div>
    <nav v-if="showNav" class="main-layout__nav" ref="navRef">
      <button v-for="(item, index) in tabs" :key="`tab-${index}`" class="nav-item" :class="getNavItemClass(item, index)"
        type="button" @click="handleTabClick(index)" :aria-label="item.title || '导航'"
        :aria-current="activeTab === index ? 'page' : undefined">
        <div class="nav-item__icon-wrapper">
          <img class="nav-item__icon" :class="getIconClass(item)" :src="getIconSrc(item, index)" :alt="item.title || ''"
            loading="eager" decoding="async">
        </div>
        <span v-if="item.title" class="nav-item__text" :class="getTextClass(index)">
          {{ item.title }}
        </span>
      </button>
    </nav>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getBizCode } from '@/utils/curEnv'
import { getTabConfig, getAllImages } from './tabConfigs'

const props = defineProps({
  scroll: {
    type: String,
    default: 'auto'
  },
  showNav: {
    type: Boolean,
    default: true
  }
})

// 使用 toRefs 解构 props
const { scroll, showNav } = toRefs(props)

const router = useRouter()
const route = useRoute()


const navRef = ref(null)

const preloadImages = () => {
  const allImages = getAllImages()
  allImages.forEach(src => {
    const img = new Image()
    img.src = src
  })
}

const tabs = ref([])

const bizCode = computed(() => getBizCode() || '')

const activeTab = computed(() => {
  const path = route.path
  return tabs.value.findIndex(item => path.indexOf(item.path) === 0)
})

const contentClass = computed(() => {
  const scrollValue = showNav.value ? scroll.value : 'none'
  const classes = [`main-layout__content--${scrollValue}`]
  if (!showNav.value) {
    classes.push('main-layout__content--no-nav')
  }

  return classes
})

const getNavItemClass = (item, index) => {
  const classes = []
  if (item.className) classes.push('nav-item--promotion')
  if (activeTab.value === index) classes.push('nav-item--active')
  return classes
}

const getIconClass = (item) => {
  const classes = []
  if (!item.className) classes.push('nav-item__icon--normal')
  if (!item.title) classes.push('nav-item__icon--large')
  return classes
}

const getIconSrc = (item, index) => {
  return activeTab.value === index ? item.activeImg : item.img
}

const getTextClass = (index) => {
  const classes = []
  if (activeTab.value === index) {
    classes.push('nav-item__text--active')
    if (bizCode.value === 'ygjd') {
      classes.push('nav-item__text--jd')
    }
  }
  return classes
}

const handleTabClick = (index) => {
  if (tabs.value.length > 4 && index === 2) {
    window.location.href = tabs.value[index].path
    return
  }
  if (activeTab.value === index) return
  const targetPath = tabs.value[index].path
  if (targetPath === '/home') {
    router.push({ path: targetPath })
  } else if (targetPath === '/category') {
    router.push({ path: targetPath, query: { _t: Date.now().toString() } })
  } else {
    router.push({ path: targetPath })
  }
}
const getPromotionIcon = async () => {
  if (getBizCode() !== 'ziying') return
}

onMounted(() => {
  preloadImages()
  const bizCode = getBizCode()
  tabs.value = getTabConfig(bizCode)
  getPromotionIcon()
})
</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}
// 安卓8以下单独处理
.android_8 {
  .main-layout {
    display: flex;
    height: 100vh;

    .main-layout__content {
      flex: 1;
      overflow: hidden;
      padding-bottom: 50px;

      &--none {
        height: calc(100vh - 50px);
      }

      &--no-nav {
        height: 100vh;
        padding-bottom: 0;
      }
    }

    .main-layout__nav {
      height: 50px;
    }
  }
}

.main-layout {
  display: flex;
  height: 100vh;
  flex-direction: column;

  &__content {
    flex: 1;
    overflow: hidden;
    padding-bottom: 50px;

    &--none {
      height: calc(100vh - 50px - var(--saib));
      padding-bottom: calc(var(--saib));
    }

    &--auto {
      height: calc(100vh - 50px - var(--saib));
      padding-bottom: 50px;
    }

    &--no-nav {
      height: 100vh;
      padding-bottom: calc(var(--saib));
    }
  }

  &__nav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    display: flex;
    height: 50px;
    border-top: 1px solid @divider-color-base;
    background: @bg-color-white;
    transform: translateZ(0);
    will-change: transform;
  }
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: @padding-6;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  transition: opacity @transition-base ease;

  &:active {
    opacity: @opacity-70;
  }

  &--promotion {
    padding: 0;
  }

  &__icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__icon {
    display: block;
    image-rendering: -webkit-optimize-contrast;
    backface-visibility: hidden;
    transform: translateZ(0);

    &--normal {
      width: 23px;
      height: 23px;
    }

    &--large {
      width: 40px;
      height: 40px;
    }
  }

  &__text {
    margin: @margin-2 0 0;
    font-size: @font-size-12;
    color: @text-color-tertiary;
    line-height: 1;
    font-feature-settings: 'tnum';

    &--active {
      color: @theme-color;
    }

    &--jd {
      color: @color-error !important;
    }
  }

  &--promotion &__icon {
    width: 60px;
    height: 50px;
  }
}

.nav-item__icon {
  opacity: 1;
  transition: opacity 0.1s ease;
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .nav-item__icon {
    image-rendering: -webkit-optimize-contrast;
  }
}
</style>
