<template>
  <div>
    <!-- 筛选触发按钮 -->
    <div class="global-filtering" @click="showDrawer = true" v-if="shouldShow">
      <div class="global-filtering-title">{{ title }}</div>
      <img class="global-filtering-icon" src="@/static/images/filtering.png" alt="" srcset="">
    </div>

    <!-- 省分服务选择器 -->
    <ProvinceServiceSelector v-model="showDrawer" @confirm="handleSelectionConfirm" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ProvinceServiceSelector from '@components/ZQCommon/ZQSelectFilter/Index.vue'
import { getEnterpriseManagerInfo } from '@/utils/zqInfo'

const props = defineProps({
  title: {
    type: String,
    default: '省分在售商品'
  },
  roleType: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['confirm'])

const showDrawer = ref(false)

// 计算是否显示筛选按钮
const shouldShow = computed(() => {
  if (!props.visible) return false

  // 如果传入了 roleType，使用传入的值
  if (props.roleType) {
    return props.roleType === '4'
  }

  // 否则从工具函数获取
  const { roleType: rt = '' } = getEnterpriseManagerInfo() || {}

  return rt === '4'
})

// 处理选择确认
const handleSelectionConfirm = (selection) => {
  emit('confirm', selection)
}
</script>

<style scoped lang="less">
.global-filtering {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px;
  background: #fff;

  .global-filtering-title {
    font-size: 15px;
    color: #5A6066;
  }

  .global-filtering-icon {
    margin-left: 10px;
    width: 18px;
    height: 18px;
  }
}
</style>
