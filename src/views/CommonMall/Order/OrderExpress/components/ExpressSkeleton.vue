<template>
  <section class="express-skeleton">
    <ol class="express-skeleton__timeline">
      <li class="express-skeleton__item" v-for="n in skeletonCount" :key="n">
        <div class="express-skeleton__text"></div>
        <div class="express-skeleton__time"></div>
      </li>
    </ol>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  skeletonCount: {
    type: Number,
    default: 3
  }
})

const { skeletonCount } = toRefs(props)
</script>

<style lang="less" scoped>
.express-skeleton {
  padding: 15px 15px 15px 56px;
  width: 100%;
  background: @bg-color-white;
  box-sizing: border-box;
  contain: layout style;

  &__timeline {
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateZ(0);
  }

  &__item {
    position: relative;
    padding-bottom: 22px;
    font-size: @font-size-14;
    line-height: 20px;
    text-align: left;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 8px;
      height: 8px;
      background-color: #e0e0e0;
      border: 2px solid @bg-color-white;
      border-radius: 50%;
      animation: skeleton-pulse 1.5s infinite;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      left: -26px;
      top: 0;
      display: block;
      width: 1px;
      height: 100%;
      background: @divider-color-base;
    }

    &:last-child::after {
      display: none;
    }
  }

  &__text {
    height: 20px;
    width: 85%;
    margin-bottom: 4px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    border-radius: @radius-2;

    &:nth-child(odd) {
      width: 75%;
    }
  }

  &__time {
    height: 12px;
    width: 120px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    border-radius: @radius-2;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
