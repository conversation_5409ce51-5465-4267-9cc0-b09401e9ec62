<template>
  <header class="express-header">
    <dl class="express-header__info-list">
      <div class="express-header__info-item">
        <dt class="express-header__label">订单号：</dt>
        <dd class="express-header__value">{{ orderId }}</dd>
      </div>
      <div class="express-header__info-item">
        <dt class="express-header__label">承运商：</dt>
        <dd class="express-header__value">{{ expressName }}</dd>
      </div>
      <div class="express-header__info-item">
        <dt class="express-header__label">运单号：</dt>
        <dd class="express-header__value">
          <span class="express-header__tracking-number">{{ expressNo }}</span>
          <button 
            v-if="shouldShowCopyButton" 
            class="express-header__copy-btn" 
            @click="handleCopy"
            aria-label="复制运单号"
          >
            复制
          </button>
        </dd>
      </div>
    </dl>
  </header>
</template>

<script setup>
import { computed, toRefs } from 'vue'

const props = defineProps({
  orderId: {
    type: String,
    default: ''
  },
  expressName: {
    type: String,
    default: '--'
  },
  expressNo: {
    type: String,
    default: '--'
  }
})

const emit = defineEmits(['copy'])

const { orderId, expressName, expressNo } = toRefs(props)

const shouldShowCopyButton = computed(() => 
  expressNo.value && expressNo.value !== '--'
)

const handleCopy = () => {
  emit('copy', expressNo.value)
}
</script>

<style lang="less" scoped>
.express-header {
  contain: layout style;
  padding: 10px;
  width: 100%;
  margin-bottom: 10px;
  line-height: 15px;
  border-bottom: 9px solid @bg-color-gray;
  font-size: @font-size-13;
  color: @text-color-primary;
  box-sizing: border-box;

  &__info-list {
    margin: 0;
  }

  &__info-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    margin: 0;
    color: @text-color-primary;
    font-weight: normal;
  }

  &__value {
    margin: 0;
    color: @text-color-primary;
    display: flex;
    align-items: center;
  }

  &__tracking-number {
    margin-right: 10px;
  }

  &__copy-btn {
    margin-left: 10px;
    padding: 0;
    width: 48px;
    height: 23px;
    line-height: 23px;
    border: 1px solid @color-orange;
    border-radius: @radius-2;
    background: transparent;
    font-size: @font-size-13;
    font-style: normal;
    text-align: center;
    color: @color-orange;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      background-color: @color-orange;
      color: @bg-color-white;
    }
  }
}
</style>
