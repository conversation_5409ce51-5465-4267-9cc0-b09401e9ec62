<template>
  <section class="express-empty">
    <div class="express-empty__container">
      <div class="express-empty__icon" role="img" aria-label="暂无数据"></div>
      <p class="express-empty__text">暂无消息~</p>
    </div>
  </section>
</template>

<script setup>
</script>

<style lang="less" scoped>
.express-empty {
  padding-left: 17px;
  text-align: center;
  background: @bg-color-white;
  box-sizing: border-box;

  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }

  &__icon {
    margin: 31px auto 20px;
    width: 208px;
    height: 161px;
    background: @bg-color-white url(../assets/empty.png) no-repeat center;
    background-size: contain;

    &::before {
      content: '';
      display: block;
      background-image: url('../assets/empty.png');
      width: 0;
      height: 0;
      opacity: 0;
    }
  }

  &__text {
    line-height: 1.3;
    font-size: @font-size-13;
    color: @text-color-tertiary;
    text-align: center;
    margin: 0;
  }
}
</style>
