<template>
  <div class="goods-card">
    <img 
      class="goods-card__image"
      :src="imageUrl" 
      loading="lazy"
      decoding="async" 
      :alt="altText"
    />
    <div 
      v-if="tagType" 
      class="goods-card__tag"
      :class="`goods-card__tag--${tagType}`"
    >
      {{ tagText }}
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import { get } from 'lodash-es'

const props = defineProps({
  goods: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    default: 0
  }
})

const { goods, index } = toRefs(props)

const imageUrl = computed(() => 
  get(goods.value, 'sku.detailImageUrl') || get(goods.value, 'sku.listImageUrl')
)

const altText = computed(() => `商品${index.value + 1}`)

const skuType = computed(() => get(goods.value, 'sku.skuType'))

const tagType = computed(() => {
  if (skuType.value === '6') return 'gift'
  if (skuType.value === '7') return 'accessory'
  return null
})

const tagText = computed(() => {
  if (tagType.value === 'gift') return '赠品'
  if (tagType.value === 'accessory') return '附件'
  return ''
})
</script>

<style lang="less" scoped>
.goods-card {
  position: relative;

  &__image {
    position: relative;
    display: block;
    width: 60px;
    height: 60px;
    line-height: 1;
    font-size: @font-size-12;
    background-color: #eee;
    contain: layout style paint;
    image-rendering: -webkit-optimize-contrast;
    aspect-ratio: 1;
    object-fit: cover;
    border-radius: @radius-2;
  }

  &__tag {
    box-sizing: border-box;
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 3px;
    background-color: #f2f2f2;
    font-size: @font-size-11;
    border: 1px solid @color-orange;
    border-radius: @radius-2;

    &--gift {
      color: @color-red;
    }

    &--accessory {
      color: @color-orange;
    }
  }
}
</style>
