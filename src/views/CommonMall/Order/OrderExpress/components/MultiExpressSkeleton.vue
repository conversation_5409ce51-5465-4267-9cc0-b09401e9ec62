<template>
  <div class="multi-express-skeleton">
    <div class="multi-express-skeleton__notice"></div>
    <div class="multi-express-skeleton__item" v-for="n in itemCount" :key="n">
      <div class="multi-express-skeleton__header"></div>
      <div class="multi-express-skeleton__content"></div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  itemCount: {
    type: Number,
    default: 2
  }
})

const { itemCount } = toRefs(props)
</script>

<style lang="less" scoped>
.multi-express-skeleton {
  &__notice {
    height: 34px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    margin-bottom: 10px;
    border-radius: @radius-2;
  }

  &__item {
    background-color: @bg-color-white;
    margin-bottom: 10px;
    border-radius: @radius-4;
  }

  &__header {
    height: 50px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    margin: 0 17px;
    border-bottom: 1px solid #e5e5e5a3;
    border-radius: @radius-2;
  }

  &__content {
    height: 90px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    margin: 15px;
    border-radius: @radius-2;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
