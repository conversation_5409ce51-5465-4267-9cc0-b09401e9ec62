<template>
  <main class="express-page">
    <ExpressHeader
      :order-id="orderId"
      :express-name="displayData.expressName"
      :express-no="displayData.expressNo"
      @copy="handleCopyExpressNo"
    />

    <ExpressTimeline
      v-if="orderTrackStatus === 1"
      :tracking-data="orderTrack"
    />

    <ExpressQueryGuide v-else-if="orderTrackStatus === 2" />

    <ExpressEmptyState v-else-if="orderTrackStatus === 0" />

    <ExpressSkeleton v-else :skeleton-count="3" />
  </main>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, shallowRef } from 'vue'
import { useRoute } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { getExpress } from '@api/interface/order.js'
import useClipboard from 'vue-clipboard3'
import { isEmpty, get, defaults, memoize } from 'lodash-es'
import ExpressHeader from './components/ExpressHeader.vue'
import ExpressTimeline from './components/ExpressTimeline.vue'
import ExpressQueryGuide from './components/ExpressQueryGuide.vue'
import ExpressEmptyState from './components/ExpressEmptyState.vue'
import ExpressSkeleton from './components/ExpressSkeleton.vue'
import {getCustomerManagerInfo, getEnterpriseManagerInfo} from "@utils/zqInfo.js";
import { getBizCode } from '@utils/curEnv.js'

const orderId = ref('')
const expressName = ref('--')
const expressNo = ref('--')
const orderTrack = shallowRef([])
const orderTrackStatus = ref(-1)
const isLoading = ref(true)

const route = useRoute()
const { toClipboard } = useClipboard()

const routeParams = computed(() =>
  defaults(route.query, {
    orderId: '',
    supplierSubOrderId: '',
    expressNo: ''
  })
)

const displayData = computed(() => ({
  expressName: expressName.value || '--',
  expressNo: expressNo.value || '--'
}))

const initializeBasicInfo = memoize(() => {
  orderId.value = get(routeParams.value, 'orderId', '')
})

const getExpressData = async (orderIdParam, expressNoParam, roleType) => {
  try {
    const bizCode = getBizCode()
    const roleTypeParam = bizCode === 'zq' ? roleType : ''
    const [err, json] = await getExpress(orderIdParam, expressNoParam, roleTypeParam)
    return err ? {} : json
  } catch (error) {
    console.error('获取快递信息失败:', error)
    return {}
  }
}

const updateExpressInfo = (deliverInfo) => {
  const safeDeliverInfo = defaults(deliverInfo, {
    expressName: '--',
    expressNo: '--',
    orderTrack: []
  })

  expressName.value = safeDeliverInfo.expressName
  expressNo.value = safeDeliverInfo.expressNo
  orderTrack.value = safeDeliverInfo.orderTrack

  if (!isEmpty(deliverInfo) && safeDeliverInfo?.orderTrack?.length > 0) {
    orderTrackStatus.value = 1
  } else if (!isEmpty(deliverInfo) && (safeDeliverInfo.expressName !== '--' || safeDeliverInfo.expressNo !== '--')) {
    orderTrackStatus.value = 2
  } else {
    orderTrackStatus.value = 0
  }
}

const handleCopyExpressNo = async (expressNumber) => {
  try {
    await toClipboard(expressNumber)
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})
onMounted(async () => {
  initializeBasicInfo()
  await nextTick()

  try {
    showLoadingToast()
    const { supplierSubOrderId, expressNo: expressNoParam } = routeParams.value
    const deliverInfo = await getExpressData(supplierSubOrderId, expressNoParam, roleType.value)
    updateExpressInfo(deliverInfo)
  } catch (error) {
    console.error('加载数据失败:', error)
    orderTrackStatus.value = 0
  } finally {
    isLoading.value = false
    closeToast()
  }
})

onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.express-page {
  width: 100%;
  min-height: 100vh;
  background: @bg-color-white;
  will-change: transform;
  contain: layout style;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
