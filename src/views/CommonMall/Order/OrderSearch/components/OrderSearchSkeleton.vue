<template>
  <div class="order-search-skeleton">
    <article 
      v-for="i in 3" 
      :key="`skeleton-${i}`" 
      class="order-search-skeleton__item"
    >
      <WoCard>
        <div class="order-search-skeleton__content">
          <header class="order-search-skeleton__header">
            <div class="order-search-skeleton__number"></div>
            <div class="order-search-skeleton__status"></div>
          </header>

          <section class="order-search-skeleton__goods">
            <div class="order-search-skeleton__image"></div>
            <div class="order-search-skeleton__info">
              <div class="order-search-skeleton__title"></div>
              <div class="order-search-skeleton__subtitle"></div>
              <div class="order-search-skeleton__price"></div>
            </div>
          </section>

          <footer class="order-search-skeleton__actions">
            <div class="order-search-skeleton__button"></div>
            <div class="order-search-skeleton__button"></div>
          </footer>
        </div>
      </WoCard>
    </article>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'
</script>

<style scoped lang="less">
.order-search-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number {
    width: 120px;
    height: 12px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-4;
  }

  &__status {
    width: 60px;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-4;
  }

  &__goods {
    display: flex;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-8;
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-4;
    margin-bottom: 8px;
  }

  &__subtitle {
    width: 60%;
    height: 14px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-4;
    margin-bottom: 8px;
  }

  &__price {
    width: 40%;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-4;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-15;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
