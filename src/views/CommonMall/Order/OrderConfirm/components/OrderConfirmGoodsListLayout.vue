<template>
  <div class="goods-section">
    <!-- 按分组渲染商品 -->
    <div v-for="group in groupedGoodsList" :key="group.groupName" class="goods-group"
      :class="{ 'goods-group--card': isZQBiz && group.groupName && group.groupName !== '默认分组' }">
      <div v-if="shouldShowGroupName(group)" class="group-header">
        <img src="../../../../../assets/images/enterprise_icon.png" alt="企业图标" class="group-header__icon">
        <h3 class="group-header__title">{{ group.groupName }}</h3>
      </div>
      <WoCard>
        <div class="goods-list">
          <OrderGoodsCard v-for="item in group.items" :key="item.id" :item="item" :image-size="imageSize"
            :min-height="minHeight" :show-actions="showActions">
            <template #actions="slotProps">
              <slot name="actions" :item="slotProps.item"></slot>
            </template>
          </OrderGoodsCard>
        </div>
      </WoCard>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import { getBizCode } from '@utils/curEnv.js'

const props = defineProps({
  // 商品列表数据
  goodsList: {
    type: Array,
    required: true,
    default: () => []
  },
  // 图片尺寸
  imageSize: {
    type: Number,
    default: 90
  },
  // 最小高度
  minHeight: {
    type: Number,
    default: 135
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: false
  }
})

// 仅政企商城支持分组显示
const isZQBiz = computed(() => getBizCode() === 'zq')

// 判断是否应该显示分组名称
const shouldShowGroupName = (group) => {
  if (!isZQBiz.value) {
    return false
  }
  // 其他商城显示有效的分组名称（非默认分组）
  return group.groupName && group.groupName !== '默认分组'
}

// 计算分组后的商品列表
const groupedGoodsList = computed(() => {
  if (!props.goodsList || props.goodsList.length === 0) {
    return []
  }

  // 按 groupName 分组
  const groups = {}

  props.goodsList.forEach(item => {
    const groupName = item.rawData?.groupName || '默认分组'

    if (!groups[groupName]) {
      groups[groupName] = {
        groupName,
        items: []
      }
    }

    groups[groupName].items.push(item)
  })

  // 转换为数组并排序（可选）
  return Object.values(groups).sort((a, b) => {
    // 将"默认分组"排在最后
    if (a.groupName === '默认分组') return 1
    if (b.groupName === '默认分组') return -1
    return a.groupName.localeCompare(b.groupName)
  })
})
</script>

<style scoped lang="less">
.goods-section {
  width: 100%;

  .goods-group {
    margin-bottom: 10px;
    contain: layout style;

    &:not(:last-child) {
      margin-bottom: 12px;
    }

    // 政企商城分组卡片化样式
    &--card {
      background: @bg-color-white;
      border-radius: @radius-10;
      padding: 8px;
      margin-bottom: 12px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      border: 1px solid #f0f0f0;
    }

    .group-header {
      display: flex;
      align-items: center;
      margin: 6px 0 8px;
      padding: 0 0 6px 10px;
      border-bottom: 1px dashed #ededed;

      &__icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        flex-shrink: 0;
      }

      &__title {
        font-size: @font-size-14;
        font-weight: @font-weight-600;
        color: @text-color-primary;
        margin: 0;
      }
    }

    .goods-list {
      width: 100%;
    }
  }
}
</style>
