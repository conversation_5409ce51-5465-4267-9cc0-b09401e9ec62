<template>
  <div class="address-skeleton">
    <div class="address-skeleton__content">
      <div class="skeleton-line address-skeleton__region"></div>
      <div class="skeleton-line address-skeleton__detail"></div>
      <div class="address-skeleton__contact">
        <div class="skeleton-line address-skeleton__name"></div>
        <div class="skeleton-line address-skeleton__phone"></div>
      </div>
    </div>
    <div class="skeleton-line address-skeleton__arrow"></div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: @radius-4;
}

.address-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 13px;

  &__content {
    flex: 1;
  }

  &__region {
    height: 16px;
    width: 120px;
    margin-bottom: 6px;
  }

  &__detail {
    height: 20px;
    width: 200px;
    margin-bottom: 12px;
  }

  &__contact {
    display: flex;
    gap: 12px;
  }

  &__name {
    height: 16px;
    width: 60px;
  }

  &__phone {
    height: 16px;
    width: 100px;
  }

  &__arrow {
    width: 6px;
    height: 12px;
    margin-left: 10px;
    border-radius: @radius-2;
  }
}
</style>
