<template>
  <WoCard>
    <div class="summary-skeleton">
      <div class="summary-skeleton__row" v-for="n in 4" :key="n">
        <div class="skeleton-line summary-skeleton__label"></div>
        <div class="skeleton-line summary-skeleton__value"></div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'
</script>

<style scoped lang="less">
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: @radius-4;
}

.summary-skeleton {
  &__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid @divider-color-base;

    &:last-child {
      border-bottom: none;
    }
  }

  &__label {
    height: 16px;
    width: 80px;
  }

  &__value {
    height: 16px;
    width: 100px;
  }
}
</style>
