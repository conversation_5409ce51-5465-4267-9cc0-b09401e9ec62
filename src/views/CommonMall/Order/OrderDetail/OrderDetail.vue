<template>
  <div class="order-detail">
    <OrderDetailSkeleton v-if="loading" />

    <header v-else class="order-detail__header">
      <div class="order-detail__status">
        <h2 class="order-detail__status-title">{{ statusTitle }}</h2>
        <div class="order-detail__status-subtitle">
          <span class="order-detail__status-text" v-if="subtitleText">{{ subtitleText }}</span>
          <span class="order-detail__status-time" v-if="subtitleTime">{{ subtitleTime }}</span>
          <span class="order-detail__status-text" v-if="subtitleSuffix">{{ subtitleSuffix }}</span>
        </div>
      </div>
    </header>

    <main v-if="!loading" class="order-detail__main">
      <LogisticsStatusCard v-if="shouldShowLogisticsModule" :status-text="logisticsDisplayInfo.statusText"
        :last-track="lastOrderTrack" :show-arrow="logisticsDisplayInfo.showArrow"
        :is-completed="currentOrderStatus === '9'" :receiver-name="receiverInfo.name"
        :receiver-phone="receiverInfo.phone" :receiver-address="fullAddress" @view-logistics="handleViewLogistics" />

      <AddressInfoCard v-if="orderInfo.addressInfo && shouldShowAddressModule" :receiver-name="receiverInfo.name"
        :receiver-phone="receiverInfo.phone" :full-address="fullAddress" :address-update-state="addressUpdateState"
        :order-state="currentOrderStatus" :is-j-d="isJD" :order-info="orderInfo" :receive-data="receiveData"
        @edit-address="handleEditAddress" />

      <WoCard v-if="transformedGoodsList.length > 0" class="order-detail__goods">
        <OrderGoodsCard v-for="(goodsItem, index) in transformedGoodsList" :key="goodsItem.id || index"
          :item="goodsItem" :item-id="goodsItem.id || index" :image-size="90" :min-height="110" :show-actions="true"
          @click="handleGoodsClick(goodsItem)">
          <template #actions="{ item }">
            <WoButton v-for="action in getItemActions(item)" :key="action.key" size="small"
              :type="action.type || 'primary'" @click.stop="action.handler(item)">
              {{ action.label }}
            </WoButton>
          </template>
        </OrderGoodsCard>
      </WoCard>

      <WoCard v-if="orderInfo && bizCode !== 'zq'" class="order-detail__price">
        <InfoRow label="商品总价">
          <template #value>
            <PriceDisplay :price="orderInfo.orderPrice" size="small" />
          </template>
        </InfoRow>
        <InfoRow v-if="isJD && totalFreight" label="运费" value="运费已分摊至商品金额" />
        <InfoRow v-else label="运费">
          <template #value>
            <PriceDisplay :price="formatMoney(0)" size="small" :bold="false" />
          </template>
        </InfoRow>
        <InfoRow :label="priceLabel">
          <template #value>
            <PriceDisplay :price="orderInfo.orderPrice" size="medium" :color="priceColor" />
          </template>
        </InfoRow>
      </WoCard>

      <WoCard v-if="orderInfo" class="order-detail__info">
        <InfoRow :label="bizCode === 'zq' ? '采购单号' : '交付订单号'">
          <template #value>
            <div class="order-detail__order-number">
              <span class="order-detail__order-number-text">{{ orderInfo.bizOrderId }}</span>
              <span class="order-detail__order-number-copy" @click="copyText(orderInfo.bizOrderId)">复制</span>
            </div>
          </template>
        </InfoRow>
        <InfoRow label="收货信息" v-if="orderInfo.addressInfo && currentOrderStatus === '9' && bizCode !== 'zq'">
          <template #value>
            <div class="order-detail__receiver-info">
              <div class="order-detail__receiver-contact">{{ receiverInfo.name }} {{ receiverInfo.phone }}</div>
              <div class="order-detail__receiver-address">{{ fullAddress }}</div>
            </div>
          </template>
        </InfoRow>
        <InfoRow label="下单时间" :value="formatDate(orderInfo.orderDate)" v-if="orderInfo.orderDate" />
        <InfoRow label="交易时间" :value="formatDate(orderInfo.orderDate)" v-if="orderInfo.orderDate && bizCode !== 'zq'" />
        <InfoRow label="付款方式" value="在线支付" v-if="bizCode !== 'zq'" />
        <InfoRow label="企业信息" :value="enterpriseName" v-if="bizCode === 'zq'" />
      </WoCard>

      <DepositInfoCard v-if="hasDeposit" :deposit-amount="depositAmount" :deposit-period="depositPeriod"
        @view-deposit="handleViewDeposit" />

      <WoActionBarPlaceholder />

      <WoActionBar>
        <div class="order-detail__action-bar"
          :class="{ 'order-detail__action-bar--pending-payment': currentOrderStatus === '0' }">
          <WoButton v-for="action in getBottomActions(currentOrderStatus)" :key="action.key" :type="action.type"
            :size="currentOrderStatus === '0' ? 'large' : 'medium'" @click="action.handler">
            <template v-if="action.key === 'goToPay' && currentOrderStatus === '0'">
              <span style="margin-right: 5px;">去支付</span>
              <PriceDisplay :price="orderInfo.orderPrice" size="small" color="white" />
            </template>
            <template v-else>
              {{ action.label }}
            </template>
          </WoButton>
        </div>
      </WoActionBar>
    </main>

    <ExpirationPopup v-model:visible="expirationPopupVisible" title="" main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理" confirm-text="确定" @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef, markRaw, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { get, reduce, map, filter, some, isEmpty } from 'lodash-es'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InfoRow from '@components/Common/InfoRow.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import OrderDetailSkeleton from './components/OrderDetailSkeleton.vue'
import LogisticsStatusCard from './components/LogisticsStatusCard.vue'
import AddressInfoCard from './components/AddressInfoCard.vue'
import DepositInfoCard from './components/DepositInfoCard.vue'
import orderStatus from '@utils/orderState.js'
import { getOrderInfo, getOrderExpress, getExpress, modOrderListShow, cancelOrder, repayOrder, jdAddressCheck, jdModifyOrderAddress } from '@api/interface/order.js'
import { addOneClick } from '@api/interface/goods.js'
import { isCheckGoodsExistsBol } from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import dayjs from 'dayjs'
import useClipboard from 'vue-clipboard3'
import { formSubmit } from 'commonkit'
import {
  analyzeLogisticsStatus,
  isFinalStatus,
} from '@utils/logisticsStatusAnalyzer.js'
import { useAlert } from "@/composables/index.js";
import { useUserStore } from "@store/modules/user.js";
import { useAfterSalesStore } from "@store/modules/afterSales.js";
import ExpirationPopup from '@components/Common/ExpirationPopup/ExpirationPopup.vue';
import { useOrderAfterSalesActions } from '@/composables/useOrderAfterSalesActions.js';
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from "@utils/zqInfo.js";

const { toClipboard } = useClipboard()
const router = useRouter()
const route = useRoute()
const $alert = useAlert()
const userStore = useUserStore()
const afterSalesStore = useAfterSalesStore()

const {
  expirationPopupVisible,
  generateActionButtons
} = useOrderAfterSalesActions()

const routeParams = reactive(route.params)
const routeQuery = reactive(route.query)

const afterSalesInfo = computed(() => afterSalesStore.getAfterSalesInfo)

// 添加bizCode计算属性
const bizCode = computed(() => getBizCode())

const enterpriseName = computed(() => {
  const enterpriseInfo = getEnterpriseManagerInfo()
  return enterpriseInfo ? enterpriseInfo.ciName : '-'
})

// 物流模块显示状态：已退款(10)、已签收(9)、配送中(5)
const LOGISTICS_ALLOWED_STATUSES = markRaw(['10', '9', '5'])
// 地址模块显示状态：待付款(0)、待发货(1)、待发货(3)、已取消(2)
const ADDRESS_ALLOWED_STATUSES = markRaw(['0', '1', '3', '2'])

const shouldShowLogisticsModule = computed(() => {
  return LOGISTICS_ALLOWED_STATUSES.indexOf(currentOrderStatus.value) !== -1
})

const shouldShowAddressModule = computed(() => {
  return ADDRESS_ALLOWED_STATUSES.indexOf(currentOrderStatus.value) !== -1
})

const totalFreight = computed(() => {
  const state = currentOrderStatus.value
  // 待付款状态(0)
  if (state === '0') {
    return get(orderInfo.value, 'totalFreight', 0)
  }
  return reduce(orderDetailSupplierOrderVoList.value, (sum, item) => sum + get(item, 'freight', 0), 0)
})

const priceLabel = computed(() => {
  const status = currentOrderStatus.value
  // 待付款(0)或已取消(2)显示应付款，其他显示实付款
  return (status === '0' || status === '2') ? '应付款' : '实付款'
})

const priceColor = computed(() => {
  // 待付款状态(0)显示橙色
  return currentOrderStatus.value === '0' ? 'orange' : undefined
})

const fullAddress = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  if (!addressInfo) return ''

  const { provinceName, cityName, countyName, townName, addrDetail } = addressInfo
  const addressParts = filter([provinceName, cityName, countyName, townName, addrDetail], Boolean)
  return addressParts.join('')
})

const receiverInfo = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  return addressInfo ? {
    name: get(addressInfo, 'recName', ''),
    phone: get(addressInfo, 'recPhone', '')
  } : { name: '', phone: '' }
})

// 地址更新状态
const addressUpdateState = computed(() => {
  return get(orderInfo.value, 'addressUpdateState', '00')
})

// 收货人信息（用于地址编辑）
const receiveData = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  if (!addressInfo) return {}

  const {
    recName,
    recPhone,
    provinceName,
    cityName,
    countyName,
    townName,
    addrDetail
  } = addressInfo

  return {
    recName,
    recPhone,
    recAddress: (provinceName || '') + (cityName || '') + (countyName || '') + (townName || '') + addrDetail,
    ...addressInfo
  }
})

const formatMoney = (amount) => (amount / 100).toFixed(2)

const currentOrderStatus = ref('')
const countdown = ref({
  hours: 0,
  minutes: 30,
  seconds: 0
})

const receiptCountdown = ref({
  days: 12,
  hours: 12,
  minutes: 14
})

let countdownTimer = null

const orderId = ref(routeParams.orderId || routeQuery.orderId)
const isPay = ref(routeQuery.isPay || 0)
const orderInfo = shallowRef({})
const orderDetailSupplierOrderVoList = shallowRef([])
const isJD = ref(false)
const isZST = ref(false)
const orderExpress = shallowRef({})
const orderPackageList = shallowRef([])
const orderTrack = shallowRef([])
const lastOrderTrack = shallowRef(null)
const logisticsAnalysis = shallowRef(null)
const dataLoaded = ref(false)
const loading = ref(true)

const hasDeposit = ref(false)
const depositOrderId = ref('')
const depositAmount = ref(0)
const depositPeriod = ref(0)

const dialogShow = ref(false)
const wapay = ref({
  bizOrderId: '',
  encryptContent: '',
  wapURL: ''
})

const transformedGoodsList = computed(() => {
  return reduce(orderDetailSupplierOrderVoList.value, (result, supplierOrder) => {
    const subOrderList = get(supplierOrder, 'orderDetailSupplierSubOrderVoList', [])

    const transformedItems = map(subOrderList, subOrder => {
      const sku = get(subOrder, 'skuNumInfoList[0]')
      const skuData = get(sku, 'sku', {})

      return {
        id: `${get(skuData, 'goodsId', '')}_${get(skuData, 'skuId', '')}`,
        rawData: supplierOrder,
        subOrderRawData: subOrder,
        price: get(skuData, 'price', 0),
        quantity: get(sku, 'skuNum', 0),
        detailImageUrl: get(skuData, 'detailImageUrl[0]', ''),
        orderState: get(subOrder, 'orderState', ''),
        skuNumInfoList: [{
          sku: {
            ...skuData,
            goodsId: get(skuData, 'goodsId', ''),
            skuId: get(skuData, 'skuId', ''),
            name: get(skuData, 'name', ''),
            detailImageUrl: get(skuData, 'detailImageUrl[0]', ''),
            param: get(skuData, 'param', ''),
            param1: get(skuData, 'param1', ''),
            param2: get(skuData, 'param2', ''),
            param3: get(skuData, 'param3', ''),
            param4: get(skuData, 'param4', ''),
            price: get(skuData, 'price', 0),
          },
          skuNum: get(sku, 'skuNum', 0)
        }]
      }
    })

    return [...result, ...transformedItems]
  }, [])
})

const statusTitle = computed(() => orderStatus(currentOrderStatus.value))

const subtitleText = computed(() => {
  const status = currentOrderStatus.value
  if (status === '0') return '剩 '      // 待付款
  if (status === '5') return '还剩 '    // 配送中
  return ''
})

const subtitleTime = computed(() => {
  const status = currentOrderStatus.value
  if (status === '0') {  // 待付款
    const { hours, minutes, seconds } = countdown.value
    return `${formatTime(hours)}:${formatTime(minutes)}:${formatTime(seconds)}`
  }
  if (status === '5') {  // 配送中
    const { days, hours, minutes } = receiptCountdown.value
    return `${days}天${hours}小时${minutes}分`
  }
  return ''
})

const subtitleSuffix = computed(() => {
  const status = currentOrderStatus.value
  if (status === '0') return ' 支付关闭'        // 待付款
  if (status === '2') return '超时未支付自动关闭'  // 已取消
  if (status === '5') return ' 支付确认'        // 配送中
  return ''
})

const logisticsDisplayInfo = computed(() => {
  if (!logisticsAnalysis.value) {
    return {
      statusText: '暂无物流信息',
      statusColor: '#999999',
      showArrow: false,
      confidence: 0
    }
  }

  const analysis = logisticsAnalysis.value
  return {
    statusText: analysis.statusText,
    showArrow: !!lastOrderTrack.value,
    confidence: analysis.confidence,
    isFinal: isFinalStatus(analysis.status)
  }
})

const formatTime = (time) => time.toString().padStart(2, '0')

const formatDate = (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : ''

const copyText = async (text) => {
  try {
    await toClipboard(text)
    showToast('复制成功')
  } catch (e) {
    console.error(e)
    showToast('复制失败')
  }
}


const startCountdown = (orderData = null) => {
  clearCountdown()

  // 待付款状态(0)才启动倒计时
  if (currentOrderStatus.value === '0') {
    let remainingTime = 0

    if (orderData) {
      const orderTime = new Date(orderData.orderDate || orderData.createTime).getTime()
      const currentTime = new Date().getTime()
      const paymentTimeout = orderData.paymentTimeout || 30 * 60 * 1000

      remainingTime = Math.max(0, paymentTimeout - (currentTime - orderTime))
    } else {
      remainingTime = 30 * 60 * 1000
    }

    const totalSeconds = Math.floor(remainingTime / 1000)
    countdown.value.hours = Math.floor(totalSeconds / 3600)
    countdown.value.minutes = Math.floor((totalSeconds % 3600) / 60)
    countdown.value.seconds = totalSeconds % 60

    if (remainingTime <= 0) {
      currentOrderStatus.value = '2'  // 已取消
      return
    }

    countdownTimer = setInterval(() => {
      if (countdown.value.seconds > 0) {
        countdown.value.seconds--
      } else if (countdown.value.minutes > 0) {
        countdown.value.minutes--
        countdown.value.seconds = 59
      } else if (countdown.value.hours > 0) {
        countdown.value.hours--
        countdown.value.minutes = 59
        countdown.value.seconds = 59
      } else {
        currentOrderStatus.value = '2'  // 已取消
        clearInterval(countdownTimer)
      }
    }, 1000)
  }
}

const clearCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

const getOrderInfoData = async () => {
  loading.value = true
  showLoadingToast()

  try {
    const [err, json] = await getOrderInfo(orderId.value, isPay.value)
    closeToast()

    if (!err && json) {
      orderInfo.value = json

      hasDeposit.value = !!(orderInfo.value.bondOrderId && orderInfo.value.bondPrice && orderInfo.value.bondTerm)

      if (orderInfo.value.bondOrderId) {
        depositOrderId.value = orderInfo.value.bondPrice
      }

      if (orderInfo.value.bondPrice) {
        depositAmount.value = (+orderInfo.value.bondPrice) / 100
      }

      if (orderInfo.value.bondTerm) {
        depositPeriod.value = (+orderInfo.value.bondTerm) / 12
      }

      orderDetailSupplierOrderVoList.value = json.orderDetailSupplierOrderVoList || []

      if (orderInfo.value.orderState === '0') {
        currentOrderStatus.value = orderInfo.value.orderState
      } else {
        currentOrderStatus.value = orderDetailSupplierOrderVoList.value[0]?.orderState || orderInfo.value.orderState
      }

      isJD.value = some(orderDetailSupplierOrderVoList.value, item =>
        get(item, 'supplierCode', '').indexOf('jd_') > -1
      )

      isZST.value = some(orderDetailSupplierOrderVoList.value, item =>
        get(item, 'supplierCode', '').indexOf('zst') > -1
      )

      orderExpress.value = await getOrderExpressData()
      orderPackageList.value = orderExpress.value.orderPackageList

      if (!isEmpty(orderPackageList.value)) {
        for (const packageItem of orderPackageList.value) {
          const deliverInfo = await getExpressData(
            get(packageItem, 'deliverInfo.supplierSubOrderId'),
            get(packageItem, 'deliverInfo.expressNo')
          )

          const trackData = get(deliverInfo, 'orderTrack', [])
          if (!isEmpty(trackData)) {
            orderTrack.value = trackData
            lastOrderTrack.value = trackData[0]
            logisticsAnalysis.value = analyzeLogisticsStatus(trackData)
            break
          }
        }
      }

      dataLoaded.value = true

      startCountdown(orderInfo.value)
    } else {
      showToast('获取订单信息失败')
    }
  } catch (error) {
    showToast.clear()
    showToast('获取订单信息失败')
    console.error('获取订单信息错误:', error)
  } finally {
    loading.value = false
  }
}

const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

const getOrderExpressData = async () => {
  showLoadingToast()
  const [err, json] = await getOrderExpress(orderId.value, roleType.value)
  closeToast()
  if (!err) return json
  return {}
}

const getExpressData = async (orderIdParam, expressNo) => {
  showLoadingToast()
  const [err, json] = await getExpress(orderIdParam, expressNo, roleType.value)
  closeToast()
  if (!err) return json
  return {}
}

const handleAddToCart = async (item) => {
  try {
    const subOrder = item
    const { sku } = subOrder.skuNumInfoList[0]
    const { goodsId, skuId, supplierCode } = sku

    const params = {
      goodsId,
      skuId,
      supplierCode
    }

    if (!goodsId || !skuId) {
      showToast('部分商品无货或已下架无法添加购物车')
      return
    }

    showLoadingToast()

    const [err1, json] = await isCheckGoodsExistsBol(params)
    if (!err1) {
      if (json) {
        params.goodsId = json
      } else {
        closeToast()
        showToast('部分商品无货或已下架无法添加购物车!')
        return
      }
    } else {
      closeToast()
      showToast('部分商品无货或已下架无法添加购物车！')
      return
    }

    const info = userStore.curAddressInfo
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    const [err2] = await addOneClick({
      ...params,
      bizCode: getBizCode('ORDER'),
      addressInfo: addressInfo
    })

    closeToast()

    if (err2) {
      showToast(err2.msg || '加入购物车失败')
      return
    }

    showToast('加入购物车成功！')
  } catch (error) {
    closeToast()
    showToast('加入购物车失败，请重试')
    console.error('加购物车错误:', error)
  }
}



const handleCancelOrder = async () => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    const [err] = await cancelOrder(orderId.value)
    closeToast()
    if (!err) {
      clearCountdown()

      await getOrderInfoData()

      showToast('订单取消成功')
    } else {
      showToast(err.msg || '取消订单失败')
    }
  }

  $alert({
    title: '',
    message: '取消后将无法恢复，您确定要取消订单吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    onConfirmCallback: async () => {
      await cancelOrderFn()
    }
  })
}


const handleGoToPay = async () => {
  showLoadingToast()
  try {
    const [res, json] = await repayOrder(orderId.value)
    closeToast()

    if (res.code === '0000') {
      if (getBizCode() === 'fupin' && json.isNeedCompanyInsert === 'true') {
        dialogShow.value = true
        wapay.value.encryptContent = json.encryptContent
        wapay.value.wapURL = json.wapURL
        wapay.value.bizOrderId = json.storeOrderId
      } else {
        formSubmit(json.wapURL, { param: json.encryptContent })
      }
    } else if (res.code === '2091070302' && !isEmpty(res.data)) {
      if (some(json, ['state', '2'])) {
        showToast('您的订单中有商品已下架')
      } else if (some(json, ['state', '3'])) {
        showToast('您的订单中有无货商品')
      } else if (some(json, ['state', '4'])) {
        showToast('您的订单中有商品库存不足')
      }
    } else {
      showToast(res.msg)
    }
  } catch (error) {
    closeToast()
    console.error('支付失败:', error)
    showToast('支付失败，请重试')
  }
}

const handleDeleteOrder = async () => {
  const cancelOrderFn = async () => {
    showLoadingToast()

    try {
      if (!isEmpty(orderDetailSupplierOrderVoList.value)) {
        const deletePromises = map(orderDetailSupplierOrderVoList.value, supplierOrder =>
          modOrderListShow({
            supplierOrderId: supplierOrder.id,
            isDelete: 1
          })
        )

        await Promise.all(deletePromises)
      } else {
        const [err] = await modOrderListShow({
          supplierOrderId: orderId.value,
          isDelete: 1
        })
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()

      clearCountdown(orderId.value)

      showToast('订单删除成功')

      setTimeout(() => {
        router.back()
      }, 1500)

    } catch (error) {
      closeToast()
      showToast(error.message || '删除失败')
    }
  }

  try {
    $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        await cancelOrderFn()
      }
    })
  } catch (error) {
    console.error('删除订单操作错误:', error)
  }
}


const handleBuyAgain = () => {
  try {
    showToast('再次购买功能待实现')
  } catch (error) {
    showToast('再次购买失败')
    console.error('再次购买错误:', error)
  }
}

const handleUrgeDelivery = () => {
  const { orderDate } = orderInfo.value
  const targetDate = dayjs(orderDate)
  const now = dayjs()
  const diff = now.diff(targetDate, 'millisecond')
  const diffInHours = diff / (1000 * 60 * 60)
  const isWithin48Hours = Math.abs(diffInHours) <= 48

  if (isWithin48Hours) {
    const dateAdd48 = targetDate.add(48, 'hour')
    const formattedDate = dateAdd48.format('M月DD日')
    $alert({
      messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:#FF780A;">${formattedDate}</span>前发货，请您耐心等待。</div>`,
      confirmButtonText: '确定',
      allowHtml: true,
      messageAlign: 'center'
    })
  } else {
    $alert({
      message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
      confirmButtonText: '确定',
      messageAlign: 'center'
    })
  }
}

const handleViewLogistics = async () => {
  const { orderDate } = orderInfo.value
  const now = dayjs()
  const orderDateDayjs = dayjs(orderDate)
  const endTimeSub180 = now.subtract(12, 'month')
  const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

  if (isWithinScope) {
    showToast('物流信息已失效 ！')
    return
  }

  try {
    const [err, orderExpress] = await getOrderExpress(orderId.value)

    if (err) {
      showToast('查询物流信息失败')
      return
    }

    const packageList = get(orderExpress, 'orderPackageList', [])
    if (!isEmpty(packageList)) {
      router.push({
        name: 'user-order-entry-express',
        params: {
          orderExpress: orderExpress
        },
        query: {
          orderId: orderId.value
        }
      })
      return
    }

    showToast('物流信息已失效 ！')
  } catch (error) {
    console.error('查询物流信息失败:', error)
    showToast('查询物流信息失败')
  }
}

const handleViewDeposit = () => {
  const url = `/itf-fi-core-web/richer/assetDetail?orderNo=${orderInfo.value.bondOrderId}&orgCode=zbbank`
  window.location.href = url
}

// 修改收货地址
const handleEditAddress = async () => {
  if (isJD.value) {
    // 京东需要特殊判断
    showLoadingToast()
    const [err] = await jdModifyOrderAddress(orderInfo.value.bizOrderId)
    closeToast()
    if (err) {
      showToast(err.msg || '地址修改检查失败')
      return
    }
  }

  router.push({
    path: '/user/order/address-edit',
    query: {
      orderId: orderInfo.value.bizOrderId,
      addressInfo: encodeURIComponent(JSON.stringify(receiveData.value))
    }
  })
}

const getItemActions = (item) => {
  return generateActionButtons(item, {
    showAddToCart: true,
    handleAddToCart: handleAddToCart,
    useSubOrderData: true,
    afterSalesInfo: afterSalesInfo.value,
    currentOrderStatus: currentOrderStatus.value
  })
}

// 处理商品点击事件，跳转到商品详情页面
const handleGoodsClick = async (goodsItem) => {
  try {
    // 从商品数据中获取 goodsId 和 skuId
    const skuNumInfoList = goodsItem.skuNumInfoList || []
    if (skuNumInfoList.length > 0) {
      const firstSku = skuNumInfoList[0].sku
      let { goodsId, skuId, supplierCode } = firstSku

      if (goodsId) {
        // 检查商品是否存在
        const params = {
          goodsId,
          skuId,
          supplierCode
        }

        showLoadingToast()
        const [err, json] = await isCheckGoodsExistsBol(params)
        closeToast()

        if (!err) {
          if (json) {
            goodsId = json
            // 跳转到商品详情页面
            router.push({
              name: 'goods-detail',
              params: {
                goodsId: goodsId,
                skuId: skuId || undefined
              }
            })
          } else {
            showToast('由于该商品已下架，为您跳转至商城首页，请您选购其他商品吧~')
            // 跳转到当前商城首页
            setTimeout(() => {
              const hostname = window.location.hostname
              if (hostname === 'epay.10010.com') {
                window.location.href = `/canary-ps-ccms-biz/home?distri_biz_code=${getBizCode()}`
              } else {
                window.location.href = `/ps-ccms-biz/home?distri_biz_code=${getBizCode()}`
              }
            }, 2000)
            return
          }
        } else {
          showToast('商品信息获取失败，请重试')
        }
      } else {
        showToast('商品信息不完整，无法查看详情')
      }
    } else {
      showToast('商品信息不完整，无法查看详情')
    }
  } catch (error) {
    closeToast()
    console.error('跳转商品详情失败:', error)
    showToast('跳转失败，请重试')
  }
}

const getBottomActions = (orderState) => {
  const actions = []

  switch (orderState) {
    case '0':  // 待付款
      actions.push(
        {
          key: 'cancelOrder',
          label: '取消订单',
          type: 'text',
          handler: handleCancelOrder
        },
        {
          key: 'goToPay',
          label: '去支付',
          type: 'gradient',
          handler: handleGoToPay
        }
      )
      break

    case '2':  // 已取消
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'gradient',
          handler: handleBuyAgain
        }
      )
      break

    case '1':  // 待发货
    case '3':  // 待发货
      actions.push(
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'secondary',
          handler: handleBuyAgain
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case '5':  // 配送中
      actions.push(
        {
          key: 'viewLogistics',
          label: '查看物流',
          type: 'secondary',
          handler: handleViewLogistics
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case '9':  // 已签收
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'viewLogistics',
          label: '查看物流',
          type: 'secondary',
          handler: handleViewLogistics
        },
        // {
        //   key: 'urgeDelivery',
        //   label: '催发货',
        //   type: 'gradient',
        //   handler: handleUrgeDelivery
        // }
      )
      break

    case '10':  // 已退款
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'gradient',
          handler: handleBuyAgain
        }
      )
      break
  }

  return actions
}

onMounted(() => {
  getOrderInfoData()
})

onUnmounted(() => {
  clearCountdown()
})
</script>

<style scoped lang="less">
.order-detail {
  min-height: 100vh;
  background-color: @bg-color-gray;

  &__header {
    width: 100%;
    height: 70px;
    background: @bg-color-white;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &__main {
    padding: 8px 10px;
    box-sizing: border-box;
  }

  &__status {
    &-title {
      margin: 0;
      font-size: @font-size-20;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      line-height: 1.5;
    }

    &-subtitle {
      font-size: @font-size-15;
      font-weight: @font-weight-400;
      line-height: 1.5;
      color: @text-color-primary;
    }

    &-time {
      color: @theme-color;
      font-weight: @font-weight-600;
    }
  }



  &__order-number {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;

    &-text {
      .ellipsis();
      max-width: calc(100% - 24px);
    }

    &-copy {
      flex-shrink: 0;
      margin-left: 8px;
      font-size: @font-size-11;
      color: @text-color-primary;
      background: #F6F6F6;
      border-radius: @radius-10;
      width: 38px;
      height: 18px;
      box-sizing: border-box;
      text-align: center;
      line-height: 18px;
      cursor: pointer;
    }
  }

  &__receiver-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    line-height: 1.5;
  }

  &__receiver-contact {
    font-size: @font-size-13;
    color: @text-color-primary;
  }

  &__receiver-address {
    flex: 1;
    font-size: @font-size-13;
    color: @text-color-primary;
    .ellipsis();
  }

  &__action-bar {
    width: 100%;
    display: flex;
    gap: 12px;
    justify-content: flex-end;

    &--pending-payment {
      justify-content: space-between;
    }
  }

}
</style>
