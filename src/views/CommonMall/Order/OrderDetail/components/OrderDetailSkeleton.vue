<template>
  <div class="order-detail-skeleton">
    <div class="order-detail-skeleton__header">
      <div class="skeleton-line skeleton-line--title"></div>
      <div class="skeleton-line skeleton-line--subtitle"></div>
    </div>

    <div class="order-detail-skeleton__content">
      <div class="skeleton-card">
        <div class="skeleton-logistics">
          <div class="skeleton-logistics__timeline">
            <div class="skeleton-circle"></div>
            <div class="skeleton-line--vertical"></div>
            <div class="skeleton-circle"></div>
          </div>
          <div class="skeleton-logistics__content">
            <div class="skeleton-line skeleton-line--logistics-title"></div>
            <div class="skeleton-line skeleton-line--logistics-detail"></div>
            <div class="skeleton-line skeleton-line--logistics-address"></div>
          </div>
        </div>
      </div>

      <div class="skeleton-card">
        <div class="skeleton-address">
          <div class="skeleton-address__header">
            <div class="skeleton-line skeleton-line--address-user"></div>
            <div class="skeleton-line skeleton-line--address-action"></div>
          </div>
          <div class="skeleton-line skeleton-line--address-detail"></div>
        </div>
      </div>

      <div class="skeleton-card">
        <div class="skeleton-goods">
          <div class="skeleton-goods__image"></div>
          <div class="skeleton-goods__info">
            <div class="skeleton-line skeleton-line--goods-title"></div>
            <div class="skeleton-line skeleton-line--goods-spec"></div>
            <div class="skeleton-line skeleton-line--goods-price"></div>
          </div>
        </div>
      </div>

      <div class="skeleton-card">
        <div class="skeleton-price">
          <div class="skeleton-line skeleton-line--price-row"></div>
          <div class="skeleton-line skeleton-line--price-row"></div>
          <div class="skeleton-line skeleton-line--price-total"></div>
        </div>
      </div>

      <div class="skeleton-card">
        <div class="skeleton-order">
          <div class="skeleton-line skeleton-line--info-row"></div>
          <div class="skeleton-line skeleton-line--info-row"></div>
          <div class="skeleton-line skeleton-line--info-row"></div>
          <div class="skeleton-line skeleton-line--info-row"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">
.order-detail-skeleton {
  min-height: 100vh;
  background-color: @bg-color-gray;

  &__header {
    width: 100%;
    height: 70px;
    background: @bg-color-white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0 20px;
    box-sizing: border-box;
  }

  &__content {
    padding: 8px 10px;
    box-sizing: border-box;
  }
}

.skeleton-card {
  background-color: @bg-color-white;
  padding: 15px;
  margin-bottom: 8px;
  border-radius: @radius-10;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: @radius-4;

  &--title {
    width: 120px;
    height: 20px;
    margin-bottom: 8px;
  }

  &--subtitle {
    width: 200px;
    height: 16px;
  }

  &--logistics-title {
    width: 150px;
    height: 16px;
    margin-bottom: 8px;
  }

  &--logistics-detail {
    width: 200px;
    height: 14px;
    margin-bottom: 6px;
  }

  &--logistics-address {
    width: 180px;
    height: 14px;
  }

  &--address-user {
    width: 120px;
    height: 16px;
  }

  &--address-action {
    width: 60px;
    height: 16px;
  }

  &--address-detail {
    width: 100%;
    height: 14px;
  }

  &--goods-title {
    width: 100%;
    height: 16px;
    margin-bottom: 8px;
  }

  &--goods-spec {
    width: 80%;
    height: 14px;
    margin-bottom: 8px;
  }

  &--goods-price {
    width: 60px;
    height: 16px;
  }

  &--price-row {
    width: 100%;
    height: 14px;
    margin-bottom: 8px;
  }

  &--price-total {
    width: 80px;
    height: 18px;
  }

  &--info-row {
    width: 100%;
    height: 14px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &--vertical {
    width: 2px;
    height: 25px;
    background: linear-gradient(180deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 100% 200%;
    animation: skeleton-loading 1.5s infinite;
    margin: 5px 0;
  }
}

.skeleton-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-logistics {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  &__timeline {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 2px;
  }

  &__content {
    flex: 1;
  }
}

.skeleton-address {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
}

.skeleton-goods {
  display: flex;
  gap: 12px;

  &__image {
    width: 90px;
    height: 90px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: @radius-4;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.skeleton-price {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-order {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-actions {
  display: flex;
  gap: 12px;
  padding: 15px;
  background-color: @bg-color-white;
  border-radius: @radius-10;
}

.skeleton-button {
  height: 42px;
  flex: 1;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: @radius-9999;

  &--primary {
    background: linear-gradient(90deg, #ffd4b3 25%, #ffb380 50%, #ffd4b3 75%);
    background-size: 200% 100%;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
