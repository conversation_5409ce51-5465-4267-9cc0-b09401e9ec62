<template>
  <WoCard>
    <div class="address-info-card">
      <div class="address-info-card__header">
        <div class="address-info-card__user">
          <img src="../../../../../static/images/address-icon.png" alt="地址图标" class="address-info-card__user-icon" />
          <span class="address-info-card__user-name">{{ receiverName }}</span>
          <span class="address-info-card__user-phone">{{ receiverPhone }}</span>
        </div>
        <div class="address-info-card__action" v-if="shouldShowAddressAction">
          <!-- 根据 addressUpdateState 显示不同内容 -->
          <div
            v-if="addressUpdateState === '01'"
            class="address-info-card__action-button"
            @click="handleEditAddress"
          >
            <img src="../../../../../static/images/mod-address-icon.png" alt="地址编辑" class="address-info-card__action-icon" />
            修改地址
          </div>
          <span v-else-if="addressUpdateState === '02'" class="address-status status-review">审核中</span>
          <span v-else-if="addressUpdateState === '03' || addressUpdateState === '06'" class="address-status status-fail">修改失败</span>
          <span v-else-if="addressUpdateState === '04'" class="address-status status-success">修改成功</span>
          <!-- addressUpdateState === '00' 或其他情况不显示任何内容 -->
        </div>
      </div>
      <div class="address-info-card__content">
        <div class="address-info-card__detail">
          {{ fullAddress }}
        </div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import { getBizCode } from '@/utils/curEnv'

const props = defineProps({
  receiverName: {
    type: String,
    default: ''
  },
  receiverPhone: {
    type: String,
    default: ''
  },
  fullAddress: {
    type: String,
    default: ''
  },
  addressUpdateState: {
    type: String,
    default: '00'
  },
  orderState: {
    type: String,
    default: ''
  },
  isJD: {
    type: Boolean,
    default: false
  },
  orderInfo: {
    type: Object,
    default: () => ({})
  },
  receiveData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['edit-address'])

const bizCode = getBizCode()
const { receiverName, receiverPhone, fullAddress, addressUpdateState, orderState, isJD, orderInfo, receiveData } = toRefs(props)

// 是否显示地址操作区域
const shouldShowAddressAction = computed(() => {
  return (orderState.value === '0' || orderState.value === '1' || orderState.value === '3') && bizCode !== 'zq'
})

// 处理地址编辑
const handleEditAddress = () => {
  emit('edit-address')
}
</script>

<style scoped lang="less">
.address-info-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;

  &__header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
  }

  &__content {
    width: 100%;
  }

  &__user {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: @font-size-16;
    color: @text-color-primary;
    font-weight: @font-weight-500;
    line-height: 1.5;
    margin-right: 8px;
    overflow: hidden;
  }

  &__user-icon {
    width: 13px;
    height: 15px;
    margin-right: 8px;
  }

  &__user-name {
    margin-right: 12px;
    .ellipsis();
  }

  &__action {
    font-size: @font-size-14;
    font-weight: @font-weight-500;
    line-height: 1.5;
  }

  &__action-button {
    display: flex;
    align-items: center;
    color: @theme-color;
    cursor: pointer;
  }

  &__action-icon {
    margin-right: 4px;
    width: 13px;
    height: 13px;
    vertical-align: middle;
  }

  &__detail {
    font-size: @font-size-12;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
    line-height: 1.5;
    .multi-ellipsis(2);
  }
}

// 地址状态样式
.address-status {
  font-size: 13px;
  font-weight: 500;
  line-height: 1.5;

  &.status-review {
    color: #999999; /* 灰色 */
  }

  &.status-fail {
    color: #FF0000; /* 红色 */
  }

  &.status-success {
    color: #00B42A; /* 绿色 */
  }
}
</style>
