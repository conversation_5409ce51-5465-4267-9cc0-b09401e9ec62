<template>
  <div class="recycle-bin-order-item" :class="{ 'recycle-bin-order-item--deleting': isDeleting }">
    <WoCard>
      <div class="recycle-bin-order-item__header">
        <div class="recycle-bin-order-item__order-info">
          <span class="recycle-bin-order-item__order-number">订单号：{{ orderId }}</span>
          <img
            src="../../../../../static/images/copy.png"
            alt="复制"
            class="recycle-bin-order-item__copy-icon"
            @click.stop="handleCopyOrder"
          />
        </div>
        <div class="recycle-bin-order-item__status" :class="statusClass">{{ statusText }}</div>
      </div>
      <div class="recycle-bin-order-item__goods">
        <OrderGoodsCard
          :key="orderId"
          :item="orderData"
          :image-size="75"
          :min-height="110"
          :showActions="true"
          :itemId="orderId"
          :showMore="false"
          @click="handleDetailClick"
        >
          <template #actions>
            <WoButton
              v-for="button in actionButtons"
              :key="button.text"
              :type="getButtonType(button.color)"
              size="small"
              @click="button.handler"
            >
              {{ button.text }}
            </WoButton>
          </template>
        </OrderGoodsCard>
      </div>
    </WoCard>
  </div>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  },
  isDeleting: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['copy-order', 'detail-click', 'permanent-delete', 'restore-order'])

const { orderData, isDeleting } = toRefs(props)

const orderId = computed(() => orderData.value.id)
const statusText = computed(() => orderData.value.statusText)
const statusClass = computed(() => orderData.value.statusClass)

const actionButtons = computed(() => [
  {
    text: '永久删除',
    color: 'default',
    handler: () => emit('permanent-delete', orderData.value)
  },
  {
    text: '还原订单',
    color: 'primary',
    handler: () => emit('restore-order', orderData.value)
  }
])

const getButtonType = (color) => {
  const typeMap = {
    primary: 'gradient',
    default: 'default'
  }
  return typeMap[color] || 'default'
}

const handleCopyOrder = () => {
  emit('copy-order', orderId.value)
}

const handleDetailClick = () => {
  emit('detail-click', orderData.value)
}
</script>

<style scoped lang="less">
.recycle-bin-order-item {
  margin-bottom: 10px;
  transition: all 0.5s ease;

  &--deleting {
    opacity: 0;
    transform: translateX(-100%);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__order-info {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__order-number {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &.status-unpaid {
      color: @theme-color;
    }

    &.status-unshipped {
      color: #2196f3;
    }

    &.status-shipped {
      color: #4caf50;
    }

    &.status-completed {
      color: @text-color-secondary;
    }
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
