<template>
  <header class="cart-header">
    <button class="cart-header__address" @click="$emit('select-address')" type="button">
      <img
        src="../../../../static/images/location.png"
        alt="地址标识"
        class="cart-header__location-icon"
        loading="eager"
        decoding="async"
        fetchpriority="high"
      />
      <span class="cart-header__address-text">{{ addressDisplay }}</span>
      <img
        src="../../../../static/images/arrow-right-black.png"
        alt="选择地址"
        class="cart-header__arrow-icon"
        loading="eager"
        decoding="async"
      />
    </button>
    <button class="cart-header__edit" @click="$emit('toggle-edit')" type="button">
      {{ isEditMode ? '完成' : '编辑' }}
    </button>
  </header>
</template>

<script setup>
import { toRefs } from 'vue'

const props = defineProps({
  addressDisplay: {
    type: String,
    required: true
  },
  isEditMode: {
    type: Boolean,
    default: false
  }
})

const { addressDisplay, isEditMode } = toRefs(props)

defineEmits(['select-address', 'toggle-edit'])
</script>

<style scoped lang="less">
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  gap: 15px;

  &__address {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
  }

  &__location-icon {
    margin-right: 5px;
    width: 11px;
    height: 12px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__address-text {
    font-size: @font-size-12;
    color: @text-color-primary;
    font-weight: @font-weight-400;
    text-align: left;
    .ellipsis();
  }

  &__arrow-icon {
    margin-left: 5px;
    width: 5px;
    height: 9px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__edit {
    flex-shrink: 0;
    font-size: @font-size-12;
    font-weight: @font-weight-600;
    color: @theme-color;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }
}
</style>
