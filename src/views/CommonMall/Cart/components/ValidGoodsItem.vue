<template>
  <van-swipe-cell class="cart-item" :disabled="item.showLongPressMenu" @open="handleSwipeOpen"
    @close="handleSwipeClose">
    <WoCard :ref="handleSetRef" class="cart-item__card" :class="{ 'cart-item__card--no-radius': item.isSwipeOpen }">
      <!-- 选择框 -->
      <div class="cart-item__checkbox" @click="handleToggleSelect">
        <img :src="checkboxSrc" alt="选择商品" class="cart-item__checkbox-icon" loading="lazy" />
      </div>

      <!-- 商品详情 -->
      <div class="cart-item__content" @click="handleContentClick" @touchstart="handleTouchStart"
        @touchend="handleTouchEnd" @touchcancel="handleTouchCancel" @touchmove="handleTouchMove">
        <!-- 商品图片 -->
        <div class="cart-item__image">
          <img :src="goodsInfo.imageUrl" :alt="goodsInfo.name" :loading="itemIndex < 3 ? 'eager' : 'lazy'"
            decoding="async" fetchpriority="high" />
        </div>

        <!-- 商品信息 -->
        <div class="cart-item__info">
          <!-- 商品名称和数量 -->
          <div class="cart-item__header">
            <h3 class="cart-item__name">{{ goodsInfo.name }}</h3>
            <div class="cart-item__quantity">
              <template v-if="!item.stepperVisible">
                <span @click.stop="handleShowStepper" class="cart-item__quantity-text">x{{ item.skuNum }}</span>
              </template>
              <van-stepper v-else v-model="localSkuNum" :min="skuNumMin" :max="skuNumMax" button-size="22px"
                @change="handleQuantityChange" @blur="handleQuantityBlur" />
            </div>
          </div>

          <!-- 商品标签 -->
          <div class="cart-item__tags">
            <div class="cart-item__spec-tags">
              <span v-for="(specItem, specIndex) in displaySpecs" :key="specIndex" class="cart-item__spec-tag">
                {{ specItem }}
              </span>
              <span v-if="isGiftsAvailable" class="cart-item__gift-tag" @click="showGiftDetailList">
                赠品
              </span>
            </div>
            <div v-if="limitInfo.text" class="cart-item__limit-tag">
              {{ limitInfo.text }}
            </div>
          </div>

          <!-- 商品价格 -->
          <div class="cart-item__price">
            <template v-if="isZqBiz && (goodsInfo.highPrice || goodsInfo.lowPrice)">
              <PriceDisplay :high-price="goodsInfo.highPrice" :low-price="goodsInfo.lowPrice" range-label="参考价" size="small"
                color="orange" />
            </template>
            <template v-else>
              <PriceDisplay :price="item.nowPrice" size="medium" color="orange" />
            </template>
          </div>
        </div>
      </div>

      <!-- 长按菜单 -->
      <div v-if="item.showLongPressMenu" class="cart-item__menu" @click="handleCloseMenu">
        <div class="cart-item__menu-overlay"></div>
        <div class="cart-item__menu-actions">
          <button class="cart-item__menu-btn cart-item__menu-btn--similar"
            @click.stop="handleLookSimilarAndCloseMenu(goodsInfo.goodsId)">
            看相似
          </button>
          <button class="cart-item__menu-btn cart-item__menu-btn--delete"
            @click.stop="handleDeleteItemAndCloseMenu(goodsInfo)">
            删除
          </button>
        </div>
      </div>
    </WoCard>

    <!-- 滑动操作按钮 -->
    <template #right>
      <div class="cart-item__swipe-actions">
        <button class="cart-item__swipe-btn cart-item__swipe-btn--similar"
          @click="handleLookSimilar(goodsInfo.goodsId)">
          看相似
        </button>
        <button class="cart-item__swipe-btn cart-item__swipe-btn--delete" @click="handleDeleteItem(goodsInfo)">
          删除
        </button>
      </div>
    </template>
  </van-swipe-cell>
</template>

<script setup>
import { computed, toRefs, ref, watch, shallowRef, onBeforeUnmount } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'
import { showToast } from 'vant'
import { getBizCode } from '@utils/curEnv.js'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  refKey: {
    type: String,
    required: true
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  isEditSelected: {
    type: Boolean,
    default: false
  },
  itemIndex: {
    type: Number,
    default: 0
  }
})

const { item, refKey, isEditMode, isEditSelected } = toRefs(props)

const emit = defineEmits([
  'toggle-select',
  'show-stepper',
  'quantity-change',
  'look-similar',
  'delete-item',
  'close-menu',
  'swipe-open',
  'swipe-close',
  'set-ref',
  'long-press',
  'gift-click',
  'content-click'
])

// 业务代码判断
const isZqBiz = computed(() => getBizCode() === 'zq')

// 选择框图片源计算
const checkboxSrc = computed(() => {
  return isEditMode.value
    ? (isEditSelected.value ? woSelectImg : noSelectImg)
    : (item.value.selected === 'true' ? woSelectImg : noSelectImg)
})

// 商品信息计算
const goodsInfo = computed(() => {
  const currentSku = item.value?.goods?.skuList?.[0] || {}
  const limitTemplate = item.value?.goods?.limitTemplate || {}

  return {
    goodsId: currentSku.goodsId,
    skuId: currentSku.skuId,
    name: currentSku.name || '',
    imageUrl: currentSku.listImageUrl || '',
    params: setNewParam(currentSku),
    lowestBuy: currentSku.lowestBuy || '1',
    limitTemplate,
    highPrice: currentSku?.highPrice || 0,  // 添加兜底值
    lowPrice: currentSku?.lowPrice || 0     // 修复拼写错误并添加兜底值
  }
})

// 显示规格标签计算
const displaySpecs = computed(() => {
  if (goodsInfo.value.params.length > 0) {
    return goodsInfo.value.params
  }
  if (Array.isArray(item.value.spec)) {
    return item.value.spec
  }
  return []
})

// 限制信息计算
const limitInfo = computed(() => {
  const goods = item.value

  if (isXG(goods, '1')) {
    return {
      text: `每人每次限购${goodsInfo.value.limitTemplate.limitNum}件`,
      type: 'perTime'
    }
  }

  if (isXG(goods, '2')) {
    return {
      text: `每人限购${goodsInfo.value.limitTemplate.limitNum}件`,
      type: 'perPerson'
    }
  }

  if (isLowestBuy(goods)) {
    return {
      text: `${goodsInfo.value.lowestBuy}件起购`,
      type: 'minimum'
    }
  }

  return { text: '', type: '' }
})

// 数量相关状态和逻辑
const localSkuNum = ref(item.value.skuNum)

// 监听数量变化
watch(() => item.value.skuNum, (newVal) => {
  localSkuNum.value = newVal
})

// 监听滑动状态，确保滑动时清除长按定时器
watch(() => item.value.isSwipeOpen, (newVal) => {
  if (newVal && touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
    isLongPressed.value = false
  }
  isSwiping.value = newVal
})

// 监听长按菜单状态，确保长按菜单显示时清除相关状态
watch(() => item.value.showLongPressMenu, (newVal) => {
  if (newVal) {
    // 长按菜单显示时，清除滑动相关状态
    isSwiping.value = false
    if (touchTimer.value) {
      clearTimeout(touchTimer.value)
      touchTimer.value = null
    }
  } else {
    // 长按菜单关闭时，重置长按状态
    isLongPressed.value = false
  }
})

// 步进器最小值
const skuNumMin = computed(() => {
  const goods = item.value
  return goods.goods.skuList[0]?.lowestBuy ? Number(goods.goods.skuList[0]?.lowestBuy) : 1
})

// 步进器最大值
const skuNumMax = computed(() => {
  const goods = item.value
  const skuNum = goods.skuNum
  const stock = goods.goods.skuList[0].stock

  if (isXG(goods, '1') || isXG(goods, '2')) {
    return +goods.goods.limitTemplate.limitNum
  }

  if (Number(skuNum) >= Number(stock)) {
    return +skuNum
  } else if (Number(skuNum) <= Number(stock)) {
    return +stock
  }

  return +stock
})

// 数量变化处理
const handleQuantityChange = (value) => {
  const diff = value - item.value.skuNum
  handleQuantityUpdate(value, diff, false)
}

// 数量失焦处理
const handleQuantityBlur = () => {
  if (localSkuNum.value !== item.value.skuNum) {
    handleQuantityUpdate(localSkuNum.value, 0, true)
  }
}

// 数量更新统一处理
const handleQuantityUpdate = (targetNum, diff, isInput) => {
  const goods = item.value
  let finalNum = targetNum
  let shouldUpdate = true
  let toastMessage = ''

  // 基础验证
  if (targetNum < 1) {
    toastMessage = '最少购买1件哦！'
    finalNum = 1
    shouldUpdate = false
  } else if (targetNum > goods.stock) {
    toastMessage = `最多购买${goods.stock}件哦！`
    finalNum = goods.stock
    shouldUpdate = false
  }

  // 起购数量验证
  if (shouldUpdate && isLowestBuy(goods)) {
    const lowestBuy = goods.goods.skuList[0]?.lowestBuy || 1

    if (isInput) {
      if (targetNum < lowestBuy) {
        toastMessage = `最少购买${lowestBuy}件哦！`
        finalNum = lowestBuy
      }
    } else {
      if (diff > 0) {
        if (goods.skuNum < lowestBuy) {
          finalNum = lowestBuy
          toastMessage = `最少购买${lowestBuy}件哦！`
        }
      } else if (diff < 0) {
        if (targetNum < lowestBuy) {
          toastMessage = `最少购买${lowestBuy}件哦！`
          shouldUpdate = false
        }
      }
    }
  }

  // 限购验证
  if (shouldUpdate && (isXG(goods, '1') || isXG(goods, '2'))) {
    const limitNum = goods.goods.limitTemplate?.limitNum || 0
    if (targetNum > limitNum) {
      toastMessage = `最多购买${limitNum}件哦！`
      finalNum = limitNum
      shouldUpdate = false
    }
  }

  // 显示提示
  if (toastMessage) {
    showToast(toastMessage)
  }

  // 更新本地显示
  if (finalNum !== localSkuNum.value) {
    localSkuNum.value = finalNum
  }

  // 触发更新事件
  if (shouldUpdate && finalNum !== goods.skuNum) {
    emit('quantity-change', { ...goods, skuNum: finalNum })
  }
}


// 赠品相关逻辑
const isGiftsAvailable = computed(() => {
  const giftList = item.value?.goods?.skuList?.[0]?.giftList
  if (!giftList || !Array.isArray(giftList) || giftList.length === 0) {
    return false
  }

  const giftBelongToSkuMinNum = giftList[0]?.belongToSkuMinNum || 99999
  const goodsNum = item.value?.skuNum || 0

  return goodsNum >= giftBelongToSkuMinNum
})

const showGiftDetailList = () => {
  const giftList = item.value?.goods?.skuList?.[0]?.giftList || []
  emit('gift-click', {
    goods: props.item,
    giftList: giftList
  })
}

// 工具方法
const setNewParam = (sku) => {
  const params = []
  if (sku.param) params.push(sku.param)
  if (sku.param1) params.push(sku.param1)
  if (sku.param2) params.push(sku.param2)
  if (sku.param3) params.push(sku.param3)
  if (sku.param4) params.push(sku.param4)
  return params
}

const isXG = (skuInfo, fix) => {
  if (skuInfo.goods.isXg === '1' && fix === '1') {
    return skuInfo.goods.limitTemplate?.limitCountType === '1' && skuInfo.goods.limitTemplate?.limitNum
  } else if (skuInfo.goods.isXg === '1' && fix === '2') {
    return skuInfo.goods.limitTemplate?.limitCountType === '2' && skuInfo.goods.limitTemplate?.limitNum
  }
  return false
}

const isLowestBuy = (skuInfo) => {
  return skuInfo.goods.skuList.some(item => item?.lowestBuy > '1')
}

// 事件处理方法
const handleToggleSelect = () => {
  emit('toggle-select', props.item)
}

const handleShowStepper = () => {
  emit('show-stepper', props.item)
}

const handleLookSimilar = (id) => {
  emit('look-similar', id)
}

const handleDeleteItem = (goodsInfo) => {
  emit('delete-item', [{
    goodsId: goodsInfo.goodsId,
    skuId: goodsInfo.skuId,
  }])
}

const handleCloseMenu = () => {
  emit('close-menu')
}

const handleSwipeOpen = () => {
  // 清除长按定时器和状态
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
  isLongPressed.value = false
  isSwiping.value = true
  emit('swipe-open', props.item)
}

const handleSwipeClose = () => {
  // 延迟重置滑动状态，避免立即触发点击事件
  setTimeout(() => {
    isSwiping.value = false
  }, 100)
  emit('swipe-close', props.item)
}

const handleSetRef = (el) => {
  emit('set-ref', el, refKey.value)
}

const handleLookSimilarAndCloseMenu = (id) => {
  emit('look-similar', id)
  emit('close-menu')
}

const handleDeleteItemAndCloseMenu = (goodsInfo) => {
  emit('delete-item', [{
    goodsId: goodsInfo.goodsId,
    skuId: goodsInfo.skuId,
  }])
  emit('close-menu')
}

// 长按事件处理
const touchTimer = shallowRef(null)
const isLongPressed = ref(false)
const isSwiping = ref(false)

const handleTouchStart = () => {
  // 如果正在滑动或已经滑动打开，不触发长按
  if (isSwiping.value || item.value.isSwipeOpen) return

  isLongPressed.value = false
  touchTimer.value = setTimeout(() => {
    isLongPressed.value = true
    emit('long-press', props.item)
  }, 500)
}

const handleTouchEnd = () => {
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

const handleTouchCancel = () => {
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

const handleTouchMove = () => {
  // 触摸移动时取消长按定时器，防止滑动时触发长按
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

// 点击商品内容区域跳转到详情页
const handleContentClick = (event) => {
  // 防止事件冒泡
  event.stopPropagation()

  // 如果刚刚长按过、正在滑动、滑动已打开或显示长按菜单，不触发点击跳转
  if (isLongPressed.value || isSwiping.value || item.value.isSwipeOpen || item.value.showLongPressMenu) {
    isLongPressed.value = false
    return
  }

  // 如果点击的是数量区域或赠品标签，不触发跳转
  const target = event.target
  if (target.closest('.cart-item__quantity') ||
    target.closest('.cart-item__gift-tag') ||
    target.closest('.van-stepper')) {
    return
  }

  emit('content-click', {
    goodsId: goodsInfo.value.goodsId,
    skuId: goodsInfo.value.skuId
  })
}

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
})
</script>

<style scoped lang="less">
// 购物车商品项组件样式 - 采用BEM命名规范
.cart-item {
  border-radius: @radius-10;
  overflow: hidden; // 确保圆角效果
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); // 添加丝滑的过渡效果

  // 优化 van-swipe-cell 的样式
  :deep(.van-swipe-cell__wrapper) {
    border-radius: @radius-10;
    transition: border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // 当滑动被禁用时的样式
  &:deep(.van-swipe-cell--disabled) {
    .van-swipe-cell__wrapper {
      transform: translateX(0) !important;
    }
  }

  // 卡片容器
  &__card {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 10px;
    transition: border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1); // 添加丝滑的圆角过渡

    :deep(.card-content) {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    // 滑动时去除右侧圆角
    &--no-radius {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  // 选择框区域
  &__checkbox {
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    &-icon {
      width: 18px;
      height: 18px;
      // 优化图片渲染
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }

  // 商品内容区域
  &__content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  // 商品图片
  &__image {
    width: 75px;
    height: 75px;
    border-radius: @radius-4;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0; // 防止图片被压缩

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // 优化图片渲染性能
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      // 使用GPU加速
      transform: translateZ(0);
      will-change: transform;
      // 优化图片加载性能
      content-visibility: auto;
      contain-intrinsic-size: 75px 75px;
    }
  }

  // 商品信息区域
  &__info {
    flex: 1;
    overflow: hidden;
    min-width: 0; // 确保flex子元素可以收缩
  }

  // 商品头部（名称和数量）
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
    gap: 10px;
  }

  // 商品名称
  &__name {
    flex: 1;
    font-size: @font-size-13;
    color: @text-color-primary;
    line-height: 1.5;
    margin: 0; // 重置h3默认margin
    font-weight: normal; // 重置h3默认font-weight
    .multi-ellipsis(2);
  }

  // 商品数量区域
  &__quantity {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    &-text {
      font-size: @font-size-14;
      color: @text-color-tertiary;
      line-height: 1.5;
      cursor: pointer;
      user-select: none;
    }
  }

  // 标签区域
  &__tags {
    margin-bottom: 5px;
  }

  // 规格标签容器
  &__spec-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px; // 使用gap替代margin
    margin-bottom: 5px;
  }

  // 规格标签
  &__spec-tag {
    display: inline-block;
    font-size: @font-size-11;
    padding: 0 5px;
    background-color: @bg-color-white;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: @radius-2;
    height: 17px;
    min-width: 16px;
    line-height: 17px;
    text-align: center;
    color: @text-color-tertiary;
    .ellipsis();
  }

  // 赠品标签
  &__gift-tag {
    display: inline-block;
    font-size: @font-size-11;
    padding: 0 5px;
    background-color: @bg-color-white;
    border: 1px solid @text-color-tips;
    border-radius: @radius-2;
    height: 17px;
    min-width: 16px;
    line-height: 17px;
    text-align: center;
    color: @text-color-tips;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.2s ease;
    .ellipsis();

    &:hover {
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // 限制标签
  &__limit-tag {
    display: inline-block;
    font-size: @font-size-11;
    color: @text-color-tips;
    .ellipsis();
  }

  // 价格区域
  &__price {
    margin-top: auto; // 价格区域置底
  }

  // 长按菜单
  &__menu {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;

    &-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: @radius-10;
      backdrop-filter: blur(2px); // 添加模糊效果
    }

    &-actions {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 50px;
      z-index: 1000;
    }

    &-btn {
      width: 58px;
      height: 58px;
      border-radius: 50%;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: @font-size-13;
      cursor: pointer;
      user-select: none;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      &--similar {
        background-color: #FF780A;
      }

      &--delete {
        background-color: #FF0F0F;
      }
    }
  }

  // 滑动操作按钮
  &__swipe-actions {
    display: flex;
    height: 100%;
    border-radius: 0 @radius-10 @radius-10 0; // 确保右侧圆角
    overflow: hidden; // 确保子元素不会超出圆角
  }

  &__swipe-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 65px;
    height: 100%;
    color: white;
    font-size: @font-size-14;
    border: none;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &--similar {
      background-color: #FF780A;
    }

    &--delete {
      background-color: #FF0F0F;
    }
  }
}

// 性能优化：使用contain属性
.cart-item__card {
  contain: layout style paint;
  // 优化渲染性能
  content-visibility: auto;
  contain-intrinsic-size: auto 95px;
}
</style>
