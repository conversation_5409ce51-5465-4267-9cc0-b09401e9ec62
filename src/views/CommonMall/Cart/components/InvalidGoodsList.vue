<template>
  <WoCard v-if="hasInvalidGoods" class="cart-goods__invalid">
    <header class="cart-goods__invalid-header">
      <h3 class="cart-goods__invalid-title">{{ invalidGoodsCount }}件失效商品</h3>
      <button class="cart-goods__invalid-action" @click="handleClearInvalidGoods" type="button">
        一键清空
      </button>
    </header>
    <div class="cart-goods__invalid-list">
      <InvalidGoodsItem v-for="item in invalidGoodsList" :key="item.cartSkuId" :item="item"
        :invalid-count="invalidGoodsCount" @toggle-select="handleToggleItemSelect"
        @look-similar="handleLookSimilar" />
    </div>
  </WoCard>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'
import InvalidGoodsItem from './InvalidGoodsItem.vue'

defineProps({
  hasInvalidGoods: {
    type: Boolean,
    required: true
  },
  invalidGoodsList: {
    type: Array,
    required: true
  },
  invalidGoodsCount: {
    type: Number,
    required: true
  }
})

const emit = defineEmits([
  'clear-invalid-goods',
  'toggle-item-select',
  'look-similar'
])

const handleClearInvalidGoods = () => {
  emit('clear-invalid-goods')
}

const handleToggleItemSelect = (item) => {
  emit('toggle-item-select', item)
}

const handleLookSimilar = (item) => {
  emit('look-similar', item)
}
</script>

<style scoped lang="less">
.cart-goods__invalid {
  margin-top: 10px;
  contain: layout style;
}

.cart-goods__invalid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.cart-goods__invalid-title {
  font-size: @font-size-14;
  font-weight: @font-weight-600;
  color: @text-color-primary;
  margin: 0;
}

.cart-goods__invalid-action {
  font-size: @font-size-14;
  color: @theme-color;
  font-weight: @font-weight-600;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  touch-action: manipulation;
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}
</style>