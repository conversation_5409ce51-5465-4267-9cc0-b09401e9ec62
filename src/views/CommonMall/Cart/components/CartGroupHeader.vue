<template>
  <header class="cart-group-header">
    <button 
      class="group-checkbox" 
      type="button"
      :class="{ 'group-checkbox--indeterminate': isIndeterminate }"
      :aria-label="isIndeterminate ? '分组部分选择，点击切换' : '分组选择，点击切换'"
      @click="handleToggleSelect">
      <img :src="checkboxSrc" alt="分组选择" class="group-checkbox__icon" />
    </button>
    <h3 class="cart-group-header__title clickable" @click="handleToggleSelect">
      {{ group.groupName }}
    </h3>
    <span class="cart-group-header__meta" aria-hidden="true">
      已选{{ selectedCount }}/{{ totalCount }}
    </span>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'

const props = defineProps({
  group: {
    type: Object,
    required: true
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  tempSelectedItems: {
    type: Set,
    default: () => new Set()
  }
})

const emit = defineEmits(['toggle-select'])

// 计算总数量
const totalCount = computed(() => props.group.goodsList?.length || 0)

// 计算已选数量
const selectedCount = computed(() => {
  if (!props.group || !Array.isArray(props.group.goodsList)) return 0
  
  return props.isEditMode
    ? props.group.goodsList.reduce((sum, item) => 
        sum + (props.tempSelectedItems.has(`${item.cartGoodsId}_${item.cartSkuId}`) ? 1 : 0), 0)
    : props.group.goodsList.reduce((sum, item) => 
        sum + (item.selected === 'true' ? 1 : 0), 0)
})

// 计算是否为半选状态
const isIndeterminate = computed(() => {
  const total = totalCount.value
  const selected = selectedCount.value
  return total > 0 && selected > 0 && selected < total
})

// 计算复选框图标
const checkboxSrc = computed(() => {
  const total = totalCount.value
  const selected = selectedCount.value
  return total > 0 && selected === total ? woSelectImg : noSelectImg
})

// 处理选择切换
const handleToggleSelect = () => {
  emit('toggle-select', props.group)
}
</script>

<style scoped lang="less">
.cart-group-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 6px 0 8px;
  padding: 0 0 6px 15px;
  border-bottom: 1px dashed #ededed;

  &__title {
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0;

    &.clickable { 
      cursor: pointer; 
      user-select: none; 
      &:active { 
        opacity: 0.7; 
      } 
    }
  }

  &__meta {
    margin-left: auto;
    font-size: @font-size-12;
    color: @text-color-tertiary;
  }
}

.group-checkbox {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  position: relative;

  &__icon {
    width: 18px;
    height: 18px;
    display: block;
  }

  &--indeterminate::after {
    content: '';
    position: absolute;
    left: 3px;
    right: 3px;
    top: 8px;
    height: 2px;
    background-color: @theme-color;
    border-radius: 2px;
    pointer-events: none;
  }
}
</style>