<template>
  <div class="after-sales-skeleton" role="status" aria-label="加载中">
    <article
      v-for="i in count"
      :key="`skeleton-${i}`"
      class="after-sales-skeleton__item"
    >
      <WoCard>
        <div class="after-sales-skeleton__content">
          <header class="after-sales-skeleton__header">
            <div class="after-sales-skeleton__order-number" aria-hidden="true"></div>
            <div class="after-sales-skeleton__status" aria-hidden="true"></div>
          </header>
          <section class="after-sales-skeleton__goods">
            <div class="after-sales-skeleton__image" aria-hidden="true"></div>
            <div class="after-sales-skeleton__info">
              <div class="after-sales-skeleton__title" aria-hidden="true"></div>
              <div class="after-sales-skeleton__subtitle" aria-hidden="true"></div>
              <div class="after-sales-skeleton__price" aria-hidden="true"></div>
            </div>
          </section>
          <footer class="after-sales-skeleton__actions">
            <div class="after-sales-skeleton__button" aria-hidden="true"></div>
            <div class="after-sales-skeleton__button" aria-hidden="true"></div>
          </footer>
        </div>
      </WoCard>
    </article>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

defineProps({
  count: {
    type: Number,
    default: 3
  }
})
</script>

<style scoped lang="less">
.after-sales-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__order-number {
    width: 120px;
    height: 12px;
    background: #f0f0f0;
    border-radius: @radius-6;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__status {
    width: 60px;
    height: 16px;
    background: #f0f0f0;
    border-radius: @radius-8;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: #f0f0f0;
    border-radius: @radius-8;
    margin-right: 12px;
    flex-shrink: 0;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__info {
    flex: 1;
    min-height: 75px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: #f0f0f0;
    border-radius: @radius-8;
    margin-bottom: 8px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__subtitle {
    width: 60%;
    height: 12px;
    background: #f0f0f0;
    border-radius: @radius-6;
    margin-bottom: 8px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__price {
    width: 40%;
    height: 14px;
    background: #f0f0f0;
    border-radius: @radius-6;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: #f0f0f0;
    border-radius: @radius-15;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }
}

@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>
