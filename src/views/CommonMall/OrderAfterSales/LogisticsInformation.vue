<template>
  <div class="logistics-information">
    <div class="wo-cell is-center is-border">
      <div class="cell-left is-require">
        <div class="left-title">物流单号</div>
      </div>
      <div class="cell-right" >
        <Field
          v-model="logicInformation.trackingNumber"
          label=""
          placeholder="请填写"
          input-align="right"
        />
      </div>
    </div>
    <div class="wo-cell is-center is-border">
      <div class="cell-left is-require">
        <div class="left-title">快递公司</div>
      </div>
      <div class="cell-right" @click="logicInfoPopupShow = true">
        <div class="right-title">{{logicInformation.expressCompany ? logicInformation.expressCompany : '请选择'}}</div>
        <img class="right-arrow" src="./assets/arrow.png" alt="" srcset="">
      </div>
    </div>
    <div class="op">
      <div class="op-btn" @click="submitApplication">
        确定
      </div>
    </div>
    <Popup class="popup logic-popup" :style="{ height: '70%' }" safe-area-inset-bottom lock-scroll round
           position="bottom" v-model:show="logicInfoPopupShow">
      <div class="popup-header">
        <p class="title">选择快递公司</p>
        <img @click="popupClose" class="close" src="./assets/popupClose.png" alt="" srcset="">
      </div>
      <div class="popup-content">
        <ul class="select-list">
          <li class="select-item" @click="logicInfoItemSelect(item)" v-for="item in logicInfoEnumList"
              :key="item.id + item.title">
            <p class="item-name">{{ item.title }}</p>
            <img v-if="item.isSelect" class="item-img" src="./assets/selectNode.png" alt="" srcset="">
            <img v-if="!item.isSelect" class="item-img" src="./assets/noSelect.png" alt="" srcset="">
          </li>
        </ul>
      </div>
    </Popup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Field, Popup, showToast } from 'vant'
import { userUploadExpressInfoForReturn } from '@api/interface/afterSales.js'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const trackingNumber = ref('')
const logicInfoPopupShow = ref(false)
const logicInfoEnumList = ref([
  {
    title: '圆通速递',
    value: '圆通速递',
    id: '4f4e8a5e-7c67-4f4e-8a5e-7c67f4e8a5e',
    index: 'Y',
    isSelect: false
  },
  {
    title: '韵达快递',
    value: '韵达快递',
    id: '4f4e8a5e-7c68-4f4e-8a5e-7c68f4e8a5e',
    index: 'Y',
    isSelect: false
  },
  {
    title: '中通快递',
    value: '中通快递',
    id: '4f4e8a5e-7c69-4f4e-8a5e-7c69f4e8a5e',
    index: 'Z',
    isSelect: false
  },
  {
    title: '顺丰速运',
    value: '顺丰速运',
    id: '4f4e8a5e-7c6a-4f4e-8a5e-7c6af4e8a5e',
    index: 'S',
    isSelect: false
  },
  {
    title: '邮政快递包裹',
    value: '邮政快递包裹',
    id: '4f4e8a5e-7c6b-4f4e-8a5e-7c6bf4e8a5e',
    index: 'Y',
    isSelect: false
  },
  {
    title: '百世快递',
    value: '百世快递',
    id: '4f4e8a5e-7c6c-4f4e-8a5e-7c6cf4e8a5e',
    index: 'B',
    isSelect: false
  },
  {
    title: '京东物流',
    value: '京东物流',
    id: '4f4e8a5e-7c6d-4f4e-8a5e-7c6df4e8a5e',
    index: 'J',
    isSelect: false
  },
  {
    title: '申通快递',
    value: '申通快递',
    id: '4f4e8a5e-7c6e-4f4e-8a5e-7c6ef4e8a5e',
    index: 'S',
    isSelect: false
  },
  {
    title: '天天快递',
    value: '天天快递',
    id: '4f4e8a5e-7c6f-4f4e-8a5e-7c6ff4e8a5e',
    index: 'T',
    isSelect: false
  },
  {
    title: 'EMS',
    value: 'EMS',
    id: '4f4e8a5e-7c70-4f4e-8a5e-7c70f4e8a5e',
    index: 'E',
    isSelect: false
  },
  {
    title: '极兔速递',
    value: '极兔速递',
    id: '4f4e8a5e-7c71-4f4e-8a5e-7c71f4e8a5e',
    index: 'J',
    isSelect: false
  },
  {
    title: '邮政标准快递',
    value: '邮政标准快递',
    id: '4f4e8a5e-7c72-4f4e-8a5e-7c72f4e8a5e',
    index: 'Y',
    isSelect: false
  },
  {
    title: '宅急送',
    value: '宅急送',
    id: '4f4e8a5e-7c73-4f4e-8a5e-7c73f4e8a5e',
    index: 'Z',
    isSelect: false
  },
  {
    title: '德邦',
    value: '德邦',
    id: '4f4e8a5e-7c74-4f4e-8a5e-7c74f4e8a5e',
    index: 'D',
    isSelect: false
  },
  {
    title: '苏宁物流',
    value: '苏宁物流',
    id: '4f4e8a5e-7c75-4f4e-8a5e-7c75f4e8a5e',
    index: 'S',
    isSelect: false
  },
  {
    title: '德邦快递',
    value: '德邦快递',
    id: '4f4e8a5e-7c76-4f4e-8a5e-7c76f4e8a5e',
    index: 'D',
    isSelect: false
  },
  {
    title: '众邮快递',
    value: '众邮快递',
    id: '4f4e8a5e-7c77-4f4e-8a5e-7c77f4e8a5e',
    index: 'Z',
    isSelect: false
  },
  {
    title: '优速快递',
    value: '优速快递',
    id: '4f4e8a5e-7c78-4f4e-8a5e-7c78f4e8a5e',
    index: 'Y',
    isSelect: false
  },
  {
    title: '百世快运',
    value: '百世快运',
    id: '4f4e8a5e-7c79-4f4e-8a5e-7c79f4e8a5e',
    index: 'B',
    isSelect: false
  },
  {
    title: '韵达快运',
    value: '韵达快运',
    id: '4f4e8a5e-7c7a-4f4e-8a5e-7c7af4e8a5e',
    index: 'Y',
    isSelect: false
  }
])

const logicInformation = ref({
  trackingNumber: '',
  expressCompany: ''
})

const submitApplication = async () => {
  if (!logicInformation.value.trackingNumber) {
    showToast('请填写物流单号！')
    return
  }
  if (!logicInformation.value.expressCompany) {
    showToast('请选择快递公司！')
    return
  }
  const params = {
    applySaleApplyId: route.query.applySaleApplyId,
    expressNo: logicInformation.value.trackingNumber,
    expressName: logicInformation.value.expressCompany,
    deliverMethod: '1'
  }
  const [err] = await userUploadExpressInfoForReturn(params)
  if (!err) {
    router.replace({
      path: '/wo-after-sales-detail',
      query: {
        afterSaleId: route.query.applySaleApplyId,
        type: 2
      }
    })
  } else {
    showToast(err.msg)
  }
}

const logicInfoItemSelect = (item) => {
  logicInfoEnumList.value.forEach(item => {
    item.isSelect = false
  })
  item.isSelect = true
  logicInformation.value.expressCompany = item.value
  logicInfoPopupShow.value = false
}

const popupClose = () => {
  logicInfoPopupShow.value = false
}
</script>

<style scoped lang="less">
.logistics-information {
  position: relative;
  padding: 0 10px;
  .module-divider {
    width: 100%;
    height: 10px;
    background: rgba(247, 247, 247, 0.80);
  }
  :deep(.van-cell){
    font-size: @font-size-15;
    padding: 0;
  }
  .wo-cell {
    width: 100%;
    min-height: 55px;
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    box-sizing: border-box;

    &.is-center {
      align-items: center;
    }

    &.is-border {
      border-bottom: 1px solid rgba(227, 227, 227, 1);
    }

    .is-require {
      position: relative;

      &:after {
        content: '*';
        position: absolute;
        top: -3px;
        right: -10px;
        color: @color-red;
        font-size: 25px;
      }
    }

    .is-vertical {
      flex-direction: column;
    }

    .cell-left {
      min-width: 65px;
      margin-right: 15px;

      .left-title {
        font-size: @font-size-16;
        color: @text-color-primary;
        line-height: 1;
        font-weight: @font-weight-400;
      }
    }

    .cell-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;

      .right-title {
        font-size: @font-size-15;
        color: @text-color-primary;
        line-height: 1;
        font-weight: @font-weight-400;
      }

      .right-arrow {
        margin-left: 5px;
        width: 15px;
        height: 15px;
      }

      .customize-content {
        .content {
          font-size: @font-size-15;
          color: @text-color-primary;
          text-align: right;
          line-height: 1.2;
          font-weight: @font-weight-400;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
          white-space: normal;
        }

        .tips {
          margin-top: 8px;
          font-size: @font-size-12;
          color: @text-color-tertiary;
          text-align: right;
          line-height: 1.2;
          font-weight: @font-weight-400;
        }
      }

      .goods-num {
        :deep(.van-stepper__input) {
          margin: 0;
          border-top: 1px solid rgba(232, 232, 232, 1);
          border-bottom: 1px solid rgba(232, 232, 232, 1);
          background-color: @bg-color-white;
        }

        :deep(.van-stepper__minus) {
          color: #000000;
          border: 1px solid rgba(232, 232, 232, 1);
          background-color: @bg-color-white;
          border-radius: 15px 0 0 15px;
        }

        :deep(.van-stepper__plus) {
          color: #000000;
          border: 1px solid rgba(232, 232, 232, 1);
          background-color: @bg-color-white;
          border-radius: 0 15px 15px 0;
        }
      }

      .goods-price {
        width: 100%;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .num {
          display: flex;
          align-items: baseline;
          font-size: @font-size-18;
          color: @theme-color;
          font-weight: @font-weight-400;
          height: 22px;
          line-height: 20px;
          .integer {
            display: flex;
            font-weight: @font-weight-600;
          }

          .decimal {
            font-size: @font-size-15;
            font-weight: @font-weight-500;
          }

          &:before {
            content: '￥';
            font-size: @font-size-13;
          }
        }

        .edit {
          margin-left: 8px;
          width: 14px;
          height: 14px;
        }
      }

      .goods-price-tips {
        margin-top: 8px;
        font-size: @font-size-12;
        color: @text-color-tertiary;
        text-align: right;
        line-height: 1;
        font-weight: @font-weight-400;
      }
    }
  }
  .op {
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    padding: 12px @padding-page * 3;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: @bg-color-white;

    .op-btn {
      width: 100%;
      height: 40px;
      background-image: @gradient-orange-106;
      border-radius: 44px;
      text-align: center;
      line-height: 40px;
      font-size: @font-size-16;
      color: @text-color-white;
      font-weight: @font-weight-500;
    }
  }
}
.popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .title {
      flex: 1;
      font-size: @font-size-17;
      color: @text-color-primary;
      text-align: center;
      line-height: 1;
      font-weight: @font-weight-400;
    }

    .close {
      width: 14px;
      height: 14px;
    }
  }

  .popup-content {
    flex: 1;
    overflow: scroll;
    .select-list {
      .select-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;

        .item-name {
          margin-right: 10px;
          font-size: @font-size-15;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
          white-space: nowrap; /* 确保文本在一行内显示 */
          overflow: hidden; /* 隐藏溢出的内容 */
          text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
        }

        .item-img {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}
</style>
