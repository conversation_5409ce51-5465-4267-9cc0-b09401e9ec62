<template>
  <div class="wish-detail">
    <div class="wish-detail-tab">
      <van-tabs v-model="activeName" @change="handleTabsChange">
        <van-tab title="申请中" name="1">
          <div class="wish-content">
            <van-list
              v-model="loading"
              :finished="finished"
              v-model:error="error"
              error-text="获取信息失败,请重新加载！"
              loading-text="加载中..."
              finished-text="没有更多了"
              :immediate-check="true"
              :offset="100"
              @load="debounceLoad"
            >
              <WishCard :wishItemData="item" v-for="item in wishList" :key="item.id"/>
            </van-list>
          </div>
        </van-tab>
        <van-tab title="已上架" name="2">
          <div class="wish-content-tips">
            如上架商品搜索不到请取消仅看有货，<span class="wish-content-tips-btn" @click="goBannerDetail">点击查看操作指导</span>。
          </div>
          <div class="wish-content wish-content-p">
            <van-list
              v-model="loading"
              :finished="finished"
              v-model:error="error"
              error-text="获取信息失败,请重新加载！"
              loading-text="加载中..."
              finished-text="没有更多了"
              :immediate-check="true"
              :offset="100"
              @load="debounceLoad"
            >
              <WishCard :wishItemData="item" v-for="item in wishList" :key="item.id"/>
            </van-list>
          </div>
        </van-tab>
        <van-tab title="未上架" name="3">
          <div class="wish-content">
            <van-list
              v-model="loading"
              :finished="finished"
              v-model:error="error"
              error-text="获取信息失败,请重新加载！"
              loading-text="加载中..."
              finished-text="没有更多了"
              :immediate-check="true"
              :offset="100"
              @load="debounceLoad"
            >
              <WishCard :wishItemData="item" v-for="item in wishList" :key="item.id"/>
            </van-list>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref,onMounted } from 'vue'
import { closeToast, showLoadingToast } from 'vant'
import { getBizCode } from '@utils/curEnv.js'
import { getWishList } from '@api/interface/wish.js'
import { debounce } from 'lodash-es'
import WishCard from './components/WishCard.vue'
import { isUnicom, isWopay } from 'commonkit'

const activeName = ref('1')
const wishList = ref([])
// 无限加载的提示判断
const onLoadGetDataLock = ref(false)
const error = ref(false)
const loading = ref(false)
const finished = ref(false)
const totalPageFlag = ref(true)
const pagination = ref({
  pageNo: 1,
  pageSize: 10,
  totalPage: 0
})

const goBannerDetail = () => {
  // 操作手册根据彩虹模版来
  if (isWopay) {
    window.location.href = '/ci-mcss-party-web/rainbow/?templateName=XYDSC&bizFrom=212&bizChannelCode=212'
  } else if (isUnicom) {
    window.location.href = '/ci-mcss-party-web/rainbow/?templateName=XYDSC&bizFrom=225&bizChannelCode=225'
  } else {
    window.location.href = '/ci-mcss-party-web/rainbow/?templateName=XYDSC&bizFrom=226&bizChannelCode=226'
  }
}

const handleTabsChange = (name) => {
  activeName.value = name
  wishList.value = []
  pagination.value.pageNo = 1
  onLoadGetDataLock.value = false
  error.value = false
  loading.value = false
  finished.value = false
  totalPageFlag.value = true
  debounceLoad()
}

const onLoad = async () => {
  if (onLoadGetDataLock.value) {
    return
  }
  const params = {
    applyState: activeName.value,
    bizCode: getBizCode(),
    ...pagination.value
  }
  onLoadGetDataLock.value = true
  showLoadingToast()
  const [err, json] = await getWishList(params)
  closeToast()
  onLoadGetDataLock.value = false
  if (!err) {
    pagination.value.pageNo++
    loading.value = false
    // 全信息的
    if (json && json.list?.length <= 0) {
      loading.value = true
      finished.value = true
      return
    }
    wishList.value = [...wishList.value, ...json.list]
    // 当前列表有多少页
    pagination.value.totalPage = json.totalPage
    if (pagination.value.pageNo > pagination.value.totalPage) {
      loading.value = true
      finished.value = true
    }
  } else {
    error.value = true
  }
}

const debounceLoad = debounce(() => {
  onLoad()
}, 500)

// 生命周期钩子
onMounted(() => {
  debounceLoad()
})
</script>

<style scoped lang="less">
.wish-detail {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f2f2f2;
}


.wish-detail-tab {
  height: 100vh;
  :deep(.van-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .van-tabs__content {
      flex: 1;
      overflow: auto;
      .van-tab__panel {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
    }
  }

  :deep(.van-tabs__line) {
    background-color: #007EE6;
  }
}

.wish-content-p {

}

.wish-content {
  padding-top: 20px;
  overflow-y: auto;
}

.wish-content-tips {
  padding: @padding-page @padding-page * 2;
  font-size: @font-size-12;
  background: #fdf3df;
  color: @text-color-primary;
  line-height: @line-height-20;
}

.wish-content-tips-btn {
  color: rgb(25, 137, 250);
}
</style>
