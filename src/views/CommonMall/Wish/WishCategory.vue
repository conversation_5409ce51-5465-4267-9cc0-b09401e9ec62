<template>
  <div class="category-container">
    <!-- 分类内容区域 -->
    <div class="category-content">
      <!-- 左侧一级分类导航 -->
      <van-sidebar v-model="activeFirstCategory" @change="handleFirstCategoryChange" ref="sidebar">
        <van-sidebar-item v-for="category in firstCategories" :key="category.id" :title="category.name"
                          ref="sidebarItems" />
      </van-sidebar>

      <!-- 右侧三级分类展示区域 -->
      <div class="third-categories-container" ref="categoryContainer">
        <!-- 骨架屏加载状态 -->
        <div v-if="isInitialLoading" class="skeleton-container">
          <div v-for="i in 3" :key="i" class="skeleton-group">
            <van-skeleton title :row="0" style="margin-bottom: 10px;" />
            <div class="skeleton-grid">
              <van-skeleton v-for="j in 4" :key="j" avatar :row="1" style="margin-bottom: 15px; width: 30%;" />
            </div>
          </div>
        </div>
        <template v-else>
          <div v-for="(group, index) in thirdCategoriesGroups" :key="index" class="category-group">
            <div class="group-title">{{ group.title }}</div>
            <div class="category-grid">
              <div v-for="item in group.items" :key="item.id" class="category-item" :style="{ width: itemWidthStyle }"
                   @click="handleCategoryClick(item)">
                <div class="category-name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import SearchHeader from "@components/Common/SearchHeader.vue"
import { getClassification } from '@api/interface/goods.js'
import { getBizCode } from "@utils/curEnv.js"

// 路由实例
const router = useRouter()
const route = useRoute()

// 搜索关键词
const searchKeyword = ref('')

// 默认图标
const defaultIcon = 'https://img01.yzcdn.cn/vant/cat.jpeg'

// 分类数据
const allCategories = ref([])
const firstCategories = ref([])
const secondCategories = ref([])
const thirdCategories = ref([])

// 当前选中的一级分类索引
const activeFirstCategory = ref(0)

// 加载状态优化
const isInitialLoading = ref(true) // 初始加载状态，用于显示骨架屏
const loading = ref(true)

// 分类容器引用
const categoryContainer = ref(null)

// 侧边栏引用
const sidebar = ref(null)
const sidebarItems = ref([])

// 每个分类项的最小宽度（像素）
const MIN_ITEM_WIDTH = 100
// 每行最少显示的项目数
const MIN_ITEMS_PER_ROW = 3

// 计算每个分类项的宽度
const itemsPerRow = ref(MIN_ITEMS_PER_ROW)
const itemWidthStyle = computed(() => {
  return `${100 / itemsPerRow.value}%`
})

// 计算每行应该显示的项目数量
const calculateItemsPerRow = () => {
  if (!categoryContainer.value) return

  // 获取容器宽度
  const containerWidth = categoryContainer.value.clientWidth
  // 计算每行可以放置的最大项目数
  const maxItemsPerRow = Math.floor(containerWidth / MIN_ITEM_WIDTH)
  // 确保至少显示最小数量的项目
  itemsPerRow.value = Math.max(maxItemsPerRow, MIN_ITEMS_PER_ROW)
}

// 将三级分类分组显示
const thirdCategoriesGroups = computed(() => {
  const groups = []

  // 按照二级分类分组
  secondCategories.value.forEach(secondCategory => {
    const items = thirdCategories.value.filter(item =>
      item.parentId === secondCategory.id
    )

    groups.push({
      id: secondCategory.id,
      title: secondCategory.name,
      items: items
    })
  })

  return groups
})

// 搜索处理函数
const handleSearch = (keyword) => {
  if (!keyword.trim()) {
    showToast('请输入搜索关键词')
    return
  }

  // 跳转到搜索结果页面
  router.push({
    path: '/goods/list',
    query: { keyword }
  })
}


// 根据分类ID查找并设置当前选中的一级分类
const findAndSetActiveCategoryById = (categoryId) => {
  if (!categoryId || !firstCategories.value.length) return false

  const index = firstCategories.value.findIndex(category => category.id === categoryId)
  if (index !== -1) {
    activeFirstCategory.value = index
    handleFirstCategoryChange(index)

    // 添加滚动到对应一级分类的功能
    nextTick(() => {
      if (sidebar.value) {
        // 获取侧边栏元素
        const sidebarEl = sidebar.value.$el
        // 获取选中的项目元素
        const selectedItem = sidebarItems.value[index]?.$el

        if (sidebarEl && selectedItem) {
          // 计算需要滚动的位置
          const itemTop = selectedItem.offsetTop
          const sidebarHeight = sidebarEl.clientHeight
          const itemHeight = selectedItem.clientHeight

          // 滚动到使当前项居中的位置
          sidebarEl.scrollTo({
            top: itemTop - (sidebarHeight / 2) + (itemHeight / 2),
            behavior: 'smooth'
          })
        }
      }
    })

    return true
  }
  return false
}

// 获取分类数据
const fetchCategories = async (id = '') => {
  try {
    // 如果是切换分类（非初始加载），使用toast loading
    if (id !== '' && !isInitialLoading.value) {
      showLoadingToast()
    }
    loading.value = true

    // 调用API获取分类数据
    const [err, data] = await getClassification({
      bizCode: 'ygjd',
      category_pid: id,
      page_no: 1,
      page_size: 500
    })
    if (err) {
      showToast('获取分类数据失败')
      return
    }

    if (data && Array.isArray(data)) {
      // 根据传入的ID判断当前获取的是哪一级分类
      if (id === '') {
        // 获取一级分类
        firstCategories.value = data.filter(item => item.depth === 1)

        // 获取路由参数中的分类ID
        const routeCategoryId = route.params.id

        // 如果有路由参数ID，则尝试查找并设置对应的一级分类
        if (routeCategoryId && firstCategories.value.length > 0) {
          // 尝试查找匹配的一级分类
          const found = findAndSetActiveCategoryById(routeCategoryId)

          // 如果没有找到匹配的一级分类，则使用默认的第一个
          if (!found) {
            activeFirstCategory.value = 0
            const firstCategoryId = firstCategories.value[0].id
            fetchSecondAndThirdCategories(firstCategoryId)
          }
        } else if (firstCategories.value.length > 0) {
          // 没有路由参数，使用默认的第一个一级分类
          activeFirstCategory.value = 0
          const firstCategoryId = firstCategories.value[0].id
          fetchSecondAndThirdCategories(firstCategoryId)
        }
      } else {
        // 获取二级和三级分类
        const secondLevel = data.filter(item => item.depth === 2)
        secondCategories.value = secondLevel

        // 如果有二级分类，获取对应的三级分类
        if (secondLevel.length > 0) {
          const secondIds = secondLevel.map(item => item.id)
          // 并行获取所有二级分类下的三级分类
          const thirdLevelPromises = secondIds.map(secondId =>
            fetchThirdCategories(secondId)
          )
          const thirdLevelResults = await Promise.all(thirdLevelPromises)

          // 合并所有三级分类结果
          thirdCategories.value = thirdLevelResults.flat().filter(Boolean)
        } else {
          thirdCategories.value = []
        }
      }
    }
  } catch (error) {
    console.error('获取分类数据出错:', error)
    showToast('获取分类数据失败')
  } finally {
    loading.value = false
    isInitialLoading.value = false
    // 关闭toast loading
    if (id !== '') {
      closeToast()
    }
  }
}

// 获取二级和三级分类
const fetchSecondAndThirdCategories = async (firstCategoryId) => {
  if (!firstCategoryId) return

  loading.value = true
  await fetchCategories(firstCategoryId)
}

// 获取三级分类
const fetchThirdCategories = async (secondCategoryId) => {
  if (!secondCategoryId) return []

  try {
    const [err, data] = await getClassification({
      bizCode: 'ygjd',
      category_pid: secondCategoryId,
      page_no: 1,
      page_size: 500
    })

    if (err || !data) return []

    return data.filter(item => item.depth === 3)
  } catch (error) {
    console.error('获取三级分类出错:', error)
    return []
  }
}

// 一级分类切换处理函数
const handleFirstCategoryChange = (index) => {
  const selectedCategory = firstCategories.value[index]
  if (!selectedCategory) return

  // 获取选中一级分类的ID
  const firstCategoryId = selectedCategory.id

  // 更新路由参数，但不重新加载页面
  router.replace({ path: `/user/wish/category/${firstCategoryId}` }, () => { }, { shallow: true })

  // 调用API获取对应的二级和三级分类
  fetchSecondAndThirdCategories(firstCategoryId)
}

// 分类项点击处理函数
const handleCategoryClick = (category) => {
  // 跳转到对应分类的商品列表页
  router.push({
    path: '/goods/list',
    query: { categoryId: category.id }
  })
}

// 监听窗口大小变化
const handleResize = () => {
  calculateItemsPerRow()
}

// 页面加载时获取分类数据并计算布局
onMounted(() => {
  fetchCategories()
  nextTick(() => {
    calculateItemsPerRow()
    window.addEventListener('resize', handleResize)
  })
})

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

// 监听数据加载完成，重新计算布局
watch(loading, (newVal) => {
  if (!newVal) {
    nextTick(() => {
      calculateItemsPerRow()
    })
  }
})
</script>

<style scoped lang="less">
.category-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.category-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧导航样式 */
:deep(.van-sidebar) {
  width: 110px;
  height: 100%;
  overflow-y: auto;
  background-color: #f7f8fa;
  text-align: center;
}

:deep(.van-sidebar-item) {
  padding: 12px 6px;
  font-size: @font-size-13;
  color: #323233;

  &--select {
    color: @theme-color;
    font-weight: 500;
    border-color: @theme-color;

    &::before {
      background-color: @theme-color;
    }
  }
}


.third-categories-container {
  flex: 1;
  padding: 15px 10px;
  overflow-y: auto;
  background-color: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;

  p {
    margin-top: 10px;
    color: #969799;
    font-size: 14px;
  }
}

.category-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 骨架屏样式
.skeleton-container {
  padding: 10px 0;
}

.skeleton-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.skeleton-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.category-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #323233;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -5px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 0 5px;
  margin-bottom: 15px;
}

.category-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 5px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.category-name {
  font-size: 12px;
  color: #646566;
  text-align: center;
  width: 100%;
  line-height: 1.2;
  .multi-ellipsis(2)
}
</style>
