<template>
  <div class="wish-card">
    <div class="wish-card-content">
      <div class="wish-card-content-item" >
        <div class="wish-card-content-group">
          <CellGroup inset>
            <Cell :title="'申请时间：' + wishItemData.createTime.split(' ')[0]" >
              <template #right-icon>
                <span style="color: #1989fa" v-if="wishItemData.applyState === '1'">申请中</span>
                <span style="color: #07c160" v-else-if="wishItemData.applyState === '2'">已上架</span>
                <span style="color: #ee0a24" v-else>上架失败</span>
              </template>
            </Cell>
            <Cell title="商品编码" :value="wishItemData.supplierGoodsId" />
            <Cell title="商品名称" :value="wishItemData.supplierGoodsName" />
            <Cell title="备注" class="wish-remark" :value="wishItemData.remark" />
            <Cell class="wish-status-failed" title="失败原因" v-if="wishItemData.applyState === '3'"  :value="wishItemData.refuseReason" />
          </CellGroup>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Cell, CellGroup } from 'vant'
import { defineProps } from 'vue'

const props = defineProps({
  wishItemData: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped lang="less">
.wish-card {
  margin-bottom: 10px;
}

.wish-card-content {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.wish-card-content-item {
  width: 100%;
  overflow-y: auto;
}

.wish-remark {
  :deep(.van-cell__value) {
    flex: auto;
    color: @text-color-primary;
    display: block !important;
    -webkit-line-clamp: 0 !important;
    overflow: visible !important;
    text-overflow: clip !important;
    line-height: @line-height-20;
  }
}

.wish-status-failed {
  :deep(.van-cell__value) {
    color: @color-red;
  }
}

:deep(.van-cell:first-child > .van-cell__title) {
  font-weight: @font-weight-700 !important;
}

:deep(.van-cell:not(:first-child)) {
  font-size: @font-size-12;
  padding: 6px 16px;

  .van-cell__title {
    min-width: 55px;
    flex: auto;
    color: @text-color-tertiary;
  }

  .van-cell__value {
    flex: auto;
    color: @text-color-primary;
    line-height: 1.5;
    .multi-ellipsis(2)
  }
}

:deep(.van-cell:not(:first-child):after) {
  border-bottom: none;
}
</style>
