<template>
  <div class="procurement-list">
    <Tabs v-model="curOrderStateIndex" class="procurement-list__tabs">
      <Tab v-for="item in orderStateList" :key="item.id" :title="item.name" />
    </Tabs>
    <div class="procurement-list__spacer" />
    <List
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      class="procurement-list__content"
      @load="handleLoad"
    >
      <article
        v-for="item in orderList"
        :key="item.id"
        class="order-item"
      >
        <header class="order-item__header">
          <span class="order-item__logo" />
          <h3 class="order-item__title">{{ item.supplier.name }}</h3>
        </header>

        <div
          v-if="shouldShowExpress(item)"
          class="order-item__express"
          @click="handleExpressClick(item)"
        >
          {{ getExpressText(item) }}
        </div>

        <div
          v-for="(orderItem, index) in item.skuNumInfoList"
          :key="index"
          class="order-item__product"
        >
          <img
            v-lazy="orderItem.sku.detailImageUrl[0]"
            class="order-item__image"
            :alt="orderItem.sku.name"
          />
          <div class="order-item__desc">
            <h4 class="order-item__name">{{ orderItem.sku.name }}</h4>
            <p class="order-item__specs">{{ orderItem.sku.param }}</p>
          </div>
          <span class="order-item__count">x{{ orderItem.skuNum }}</span>
        </div>

        <footer class="order-item__footer">
          <p class="order-item__info">商品采购单号：{{ item.bizOrderId }}</p>
          <div class="order-item__status-wrapper">
            <div class="order-item__left">
              <span
                v-if="orderStateFormat(item.orderState)"
                :class="getStateClass(item.orderState)"
                class="order-item__state"
              >
                {{ orderStateFormat(item.orderState) }}
              </span>
              <p class="order-item__info">下单时间：{{ formatDate(item.orderDate) }}</p>
            </div>
            <div v-if="item.orderState === '9'" class="order-item__divider" />
            <div v-if="shouldShowReceiveStatus(item)" class="order-item__right">
              <span :class="getReceiveStateClass(item)" class="order-item__state">
                {{ getReceiveStateText(item) }}
              </span>
              <p v-if="item.isAck === '1'" class="order-item__info">
                确认时间：{{ formatDate(item.ackTime) }}
              </p>
            </div>
          </div>
        </footer>

        <div v-if="shouldShowConfirmBtn(item)" class="order-item__actions">
          <button class="order-item__btn" @click="handleConfirmReceive(item)">
            确认客户已收货
          </button>
        </div>
      </article>
    </List>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Tab, Tabs, List } from 'vant'
import dayjs from 'dayjs'
import { getOrderList, getOrderExpress } from '@/api/interface/order'
import { managerConfirmReceive } from '@/api/interface/zq'
import { getBizCode } from '@/utils/curEnv'

const ORDER_STATE_MAP = {
  '2': '客户已取消',
  '3': '客户待发货',
  '4': '客户待发货',
  '5': '客户待签收',
  '9': '客户已签收'
}

const route = useRoute()
const router = useRouter()

const ciCode = route.query.ciCode
const orderStateList = [
  { id: '20', name: '待确认' },
  { id: '21', name: '已确认' },
  { id: '22', name: '其他' },
  { id: '', name: '全部' }
]

const curOrderStateIndex = ref(3)
const currentPage = ref(1)
const orderList = ref([])
const loading = ref(false)
const finished = ref(false)

const curOrderState = computed(() =>
  orderStateList[curOrderStateIndex.value].id
)

const formatDate = (date) => dayjs(date).format('YYYY-MM-DD')

const orderStateFormat = (state) => ORDER_STATE_MAP[state] || ''

const shouldShowExpress = (item) =>
  item.orderState === '5' || item.orderState === '9'

const getExpressText = (item) => {
  if (!item.orderExpress?.orderPackageList) return '包裹查询中...'
  const packageCount = item.orderExpress.orderPackageList.length
  return packageCount > 1
    ? `该订单已拆分${packageCount}个包裹发出，点击可查看物流`
    : '该订单包裹发出，点击可查看物流'
}

const shouldShowReceiveStatus = (item) => item.orderState === '9'

const shouldShowConfirmBtn = (item) =>
  item.orderState === '9' && item.isAck === '0'

const getStateClass = (state) => `order-item__state--${state}`

const getReceiveStateClass = (item) =>
  `order-item__state--${item.isAck === '1' ? '9' : '3'}`

const getReceiveStateText = (item) =>
  item.isAck === '1' ? '已确认客户收货' : '待确认客户收货'

const fetchOrderList = async () => {
  loading.value = true
  try {
    const params = {
      disriBiz: getBizCode('ORDER'),
      orderState: curOrderState.value,
      pageNum: currentPage.value,
      pageSize: 10,
      accountInfo: {
        role: '2',
        enterpriseCode: ciCode
      }
    }

    const [err, json] = await getOrderList(params)
    if (err) {
      console.error('获取订单列表失败:', err.msg)
      return
    }

    orderList.value = [...orderList.value, ...json.list]

    orderList.value.forEach(async (item) => {
      if (shouldShowExpress(item)) {
        const [expressErr, expressJson] = await getOrderExpress(item.id, '2')
        if (!expressErr) {
          item.orderExpress = expressJson
        }
      }
    })

    currentPage.value++
    if (json.list.length < 10) {
      finished.value = true
    }
  } finally {
    loading.value = false
  }
}

const resetAndFetch = () => {
  currentPage.value = 1
  orderList.value = []
  finished.value = false
  fetchOrderList()
}

const handleLoad = () => fetchOrderList()

const handleExpressClick = (item) => {
  router.push({
    name: 'zq-user-order-entry-express',
    query: { orderId: item.id, roleType: '2' },
    params: { orderExpress: item.orderExpress }
  })
}

const handleConfirmReceive = async (item) => {
  const [err, json] = await managerConfirmReceive(item.id)
  if (err) {
    console.error('确认收货失败:', err.msg)
    return
  }

  item.ackTime = json.actTime
  item.isAck = json.isAck
}

watch(curOrderStateIndex, resetAndFetch)
</script>

<style lang="less" scoped>
@import '@/assets/css/design-system.less';

.procurement-list {
  background-color: @bg-color-gray;

  &__tabs {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 2;
  }

  &__spacer {
    height: 50px;
  }

  &__content {
    padding: @padding-page * 2;
    min-height: calc(100vh - 50px);
  }
}

.order-item {
  margin-bottom: @padding-page * 2;
  padding: 15px 12px;
  background-color: @bg-color-white;
  border-radius: @radius-10;

  &__header {
    display: flex;
    align-items: center;
  }

  &__logo {
    width: 20px;
    height: 20px;
    background-image: url('./assets/enterprise-logo.png');
    background-size: 100%;
    flex-shrink: 0;
  }

  &__title {
    margin: 0 0 0 4px;
    line-height: 15px;
    font-size: @font-size-15;
    color: @text-color-primary;
    font-weight: @font-weight-500;
  }

  &__express {
    margin: 13px 0;
    padding: 7px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: @bg-color-gray;
    border-radius: @radius-6;
    font-size: @font-size-13;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
    cursor: pointer;

    &::after {
      content: '';
      width: 6px;
      height: 6px;
      border-top: 1px solid @text-color-tertiary;
      border-right: 1px solid @text-color-tertiary;
      transform: rotate(45deg);
      flex-shrink: 0;
    }
  }

  &__product {
    display: flex;
    margin-top: 14px;
  }

  &__image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: @radius-6;
    object-fit: cover;
  }

  &__desc {
    flex-grow: 1;
    margin-left: 8px;
  }

  &__name {
    margin: 0 0 4px 0;
    line-height: 18px;
    font-size: @font-size-13;
    color: @text-color-primary;
    font-weight: @font-weight-400;
    .multi-ellipsis(2);
  }

  &__specs {
    margin: 0;
    line-height: 12px;
    font-size: @font-size-12;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
  }

  &__count {
    flex-shrink: 0;
    min-width: 20px;
    text-align: right;
    line-height: 12px;
    font-size: @font-size-12;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
  }

  &__footer {
    margin-top: 22px;
  }

  &__info {
    margin: 8px 0;
    font-size: @font-size-13;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
  }

  &__status-wrapper {
    display: flex;
    align-items: stretch;
  }

  &__left,
  &__right {
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
  }

  &__divider {
    width: 1px;
    background-color: @divider-color-base;
    margin: 4px 10px;
  }

  &__state {
    text-align: center;
    border-radius: @radius-4;
    font-size: @font-size-12;
    font-weight: @font-weight-400;
    display: inline-block;
    padding: 2px 4px;
    width: auto;
    max-width: fit-content;
    min-width: unset;
    flex: 0 0 auto;

    &--2 {
      background: @bg-color-gray;
      color: @text-color-tertiary;
    }

    &--3,
    &--4,
    &--5 {
      background: @bg-color-tips;
      color: @color-orange;
    }

    &--9 {
      background: #EFFCF8;
      color: @color-green;
    }
  }

  &__actions {
    display: flex;
    justify-content: center;
    margin-top: 6px;
  }

  &__btn {
    width: 180px;
    height: @button-height-36;
    background: @gradient-orange-106;
    border-radius: @radius-18;
    border: 0;
    font-size: @font-size-15;
    color: @text-color-white;
    text-align: center;
    font-weight: @font-weight-400;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.procurement-list {
  .van-tabs--line .van-tabs__wrap {
    height: 50px;
  }

  .van-tab {
    color: @text-color-primary;
    font-weight: @font-weight-500;
  }

  .van-tabs__line {
    background: @color-orange;
    border-radius: @radius-2;
    width: 60px;
  }
}
</style>
