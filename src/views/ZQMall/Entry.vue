<template>
  <div v-if="noPermission" class="zq-entry">
    <img src="@/assets/images/no-p.png" alt="无权限" class="zq-entry__icon" width="200" height="200" loading="eager" decoding="async" fetchpriority="high">
    <p class="zq-entry__text">暂无该业务查看权限</p>
  </div>
</template>

<script setup>
import { onBeforeMount, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { woReport } from 'commonkit'
import { queryZqInfo } from '@/utils/zqInfo'

const noPermission = ref(false)
const route = useRoute()
const router = useRouter()

onBeforeMount(() => {
  const rawIsv = (route.query && route.query.isv) || ''
  const isv = typeof rawIsv === 'string' ? rawIsv : String(rawIsv || '')
  const zqInfo = queryZqInfo() || {}
  const role = String(zqInfo && zqInfo.roleType)
  const list = Array.isArray(zqInfo && zqInfo.isvList) ? zqInfo.isvList : []

  if (role === '1') {
    if (list.length === 1) {
      const only = list[0]
      const targetIsv = typeof only === 'string' ? only : (only && only.isvId) || ''
      router.replace({ path: '/home', query: { isv: targetIsv } })
    } else if (isv && list.some(item => (typeof item === 'string' ? item === isv : item && item.isvId === isv))) {
      router.replace({ path: '/home', query: { isv } })
    } else {
      noPermission.value = true
      woReport('政企商城入口 zqInfo 数据异常', JSON.stringify(zqInfo))
    }
  } else if (role === '4') {
    router.replace({ path: '/home', query: { isv } })
  } else if (role === '2') {
    router.replace('/zq/manager/enterprise-list')
  } else {
    noPermission.value = true
  }
})
</script>

<style lang="less" scoped>
.zq-entry {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: @bg-color-white;
}
.zq-entry__icon {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}
.zq-entry__text {
  font-size: @font-size-14;
  color: @text-color-tertiary;
  margin-bottom: 50px;
}
</style>
