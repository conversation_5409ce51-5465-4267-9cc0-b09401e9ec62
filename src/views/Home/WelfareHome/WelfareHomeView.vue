<template>
  <BaseHomeLayout
    home-class="welfare-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #additional-content>
      <div
        v-if="skeletonStates.goodsHeader || typeList.length > 0"
        class="home-goods-header-container"
      >
        <transition name="skeleton-fade" mode="out-in">
          <GoodsHeaderSkeleton v-if="skeletonStates.goodsHeader" key="goods-header-skeleton" />
          <GoodsHeader
            v-else
            :typeList="typeList"
            key="goods-header-content"
            @switchTabs="switchTabs"
          />
        </transition>
      </div>
    </template>

    <template #main-content>
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import GoodsHeader from '@views/Home/components/GoodsHeader.vue'
import GoodsHeaderSkeleton from '@views/Home/components/Skeleton/GoodsHeaderSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// 使用组合式函数
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  waterfallCurrentPage,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 页面特有数据
const typeList = ref([])
const goodsPoolIdSelected = ref('')

// 扩展骨架屏状态
skeletonStates.value = {
  ...skeletonStates.value,
  goodsHeader: true
}

moduleDataReady.value = {
  ...moduleDataReady.value,
  goodsHeader: false
}

// 瀑布流渲染完成处理
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 加载更多瀑布流商品
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 切换标签页
const switchTabs = async (id) => {
  // 不立即清空数据，避免页面回弹
  goodsPoolIdSelected.value = id
  // 重置分页状态但保留当前数据
  waterfallCurrentPage.value = 1
  waterfallFinished.value = false
  waterfallLoading.value = false
  waterfallButtonCanShow.value = false
  waterfallRenderComplete.value = false

  // 不设置骨架屏状态，直接加载新数据
  await nextTick()
  getWaterfallList(id, '', false)
}

// 切换商品池
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 初始化页面数据
const initPage = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  moduleDataReady.value.goodsHeader = true
  await hideSkeletonInOrder(['banner', 'gridMenu', 'goodsHeader'])

  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id)
  }
}

onMounted(() => {
  getHeaderBannerList()
  getIconList(5) // WelfareHome 使用 showPage: 5
  initPage()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped lang="less">
.welfare-home {
  .home-goods-header-container {
    margin: 10px 0;
    box-sizing: border-box;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
