/**
 * Vite 主题插件
 * 支持根据 bizCode 动态加载主题文件
 */

import { readFileSync, existsSync } from 'fs'
import { resolve } from 'path'

// 主题文件映射
const THEME_FILES = {
  'orange': 'theme-orange.less',
  'red': 'theme-red.less'
}

// bizCode 到主题的映射
const BIZ_CODE_THEME_MAP = {
  'unicom': 'orange',
  'default': 'orange',
  'jd': 'red',
  'jingdong': 'red'
}

/**
 * 创建主题插件
 */
export function createThemePlugin(options = {}) {
  const {
    themeDir = 'src/assets/css/theme',
    defaultTheme = 'orange',
    enableHMR = true
  } = options

  return {
    name: 'vite-theme-plugin',
    
    configResolved(config) {
      this.isProduction = config.command === 'build'
      this.root = config.root
      this.themeDir = resolve(this.root, themeDir)
    },

    configureServer(server) {
      if (!enableHMR) return

      // 监听主题文件变化
      const themeFiles = Object.values(THEME_FILES).map(file => 
        resolve(this.themeDir, file)
      )

      server.ws.on('theme:change', (data) => {
        // 处理主题切换
        console.log('Theme changed:', data)
      })

      // 添加中间件处理主题请求
      server.middlewares.use('/api/theme', (req, res, next) => {
        if (req.method === 'GET') {
          const url = new URL(req.url, `http://${req.headers.host}`)
          const bizCode = url.searchParams.get('bizCode') || 'default'
          
          const theme = this.getThemeByBizCode(bizCode)
          const themeConfig = this.getThemeConfig(theme)
          
          res.setHeader('Content-Type', 'application/json')
          res.end(JSON.stringify(themeConfig))
        } else {
          next()
        }
      })
    },

    resolveId(id) {
      // 处理主题文件的动态导入
      if (id.startsWith('virtual:theme/')) {
        return id
      }
    },

    load(id) {
      // 加载虚拟主题模块
      if (id.startsWith('virtual:theme/')) {
        const themeName = id.replace('virtual:theme/', '')
        return this.generateThemeModule(themeName)
      }
    },

    generateBundle(options, bundle) {
      if (!this.isProduction) return

      // 在生产环境中生成主题文件
      Object.entries(THEME_FILES).forEach(([themeName, fileName]) => {
        const themeFilePath = resolve(this.themeDir, fileName)
        
        if (existsSync(themeFilePath)) {
          const content = readFileSync(themeFilePath, 'utf-8')
          
          // 将主题文件添加到构建输出中
          bundle[`assets/css/theme-${themeName}.css`] = {
            type: 'asset',
            fileName: `assets/css/theme-${themeName}.css`,
            source: this.processLessContent(content)
          }
        }
      })
    },

    // 自定义方法
    getThemeByBizCode(bizCode) {
      return BIZ_CODE_THEME_MAP[bizCode] || defaultTheme
    },

    getThemeConfig(themeName) {
      const fileName = THEME_FILES[themeName]
      if (!fileName) {
        return null
      }

      const filePath = resolve(this.themeDir, fileName)
      if (!existsSync(filePath)) {
        return null
      }

      return {
        name: themeName,
        file: fileName,
        path: filePath,
        url: `/src/assets/css/theme/${fileName}`
      }
    },

    generateThemeModule(themeName) {
      const config = this.getThemeConfig(themeName)
      if (!config) {
        return `export default null`
      }

      return `
        import '${config.path}'
        
        export default {
          name: '${themeName}',
          file: '${config.file}',
          loaded: true
        }
      `
    },

    processLessContent(content) {
      // 这里可以添加 Less 编译逻辑
      // 简化版本，实际项目中需要使用 less 编译器
      return content
    }
  }
}

/**
 * 主题切换中间件
 */
export function createThemeMiddleware() {
  return (req, res, next) => {
    // 检查是否是主题相关的请求
    if (req.url.includes('/theme/')) {
      const url = new URL(req.url, `http://${req.headers.host}`)
      const bizCode = url.searchParams.get('bizCode')
      
      if (bizCode) {
        // 设置主题相关的响应头
        res.setHeader('X-Theme-BizCode', bizCode)
        res.setHeader('X-Theme-Name', BIZ_CODE_THEME_MAP[bizCode] || 'orange')
      }
    }
    
    next()
  }
}

/**
 * 生成主题配置文件
 */
export function generateThemeConfig() {
  return {
    themes: Object.entries(BIZ_CODE_THEME_MAP).map(([bizCode, theme]) => ({
      bizCode,
      theme,
      file: THEME_FILES[theme],
      url: `/src/assets/css/theme/${THEME_FILES[theme]}`
    })),
    default: defaultTheme,
    files: THEME_FILES
  }
}
