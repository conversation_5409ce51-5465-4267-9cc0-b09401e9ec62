/**
 * 主题相关的组合式函数
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import themeManager from '@/utils/themeManager.js'

/**
 * 使用主题的组合式函数
 */
export function useTheme() {
  // 响应式数据
  const currentTheme = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // 计算属性
  const themeColor = computed(() => {
    return currentTheme.value?.color || '#FF7A0A'
  })

  const themeName = computed(() => {
    return currentTheme.value?.name || 'orange'
  })

  const isOrangeTheme = computed(() => {
    return themeName.value === 'orange'
  })

  const isRedTheme = computed(() => {
    return themeName.value === 'red'
  })

  // 主题变更事件处理
  const handleThemeChange = (event) => {
    currentTheme.value = event.detail.config
    error.value = null
  }

  // 切换主题
  const switchTheme = async (bizCode) => {
    try {
      isLoading.value = true
      error.value = null
      await themeManager.switchTheme(bizCode)
    } catch (err) {
      error.value = err.message || '主题切换失败'
      console.error('主题切换失败:', err)
    } finally {
      isLoading.value = false
    }
  }

  // 获取当前主题信息
  const getCurrentTheme = () => {
    return themeManager.getCurrentTheme()
  }

  // 获取所有可用主题
  const getAvailableThemes = () => {
    return themeManager.getAvailableThemes()
  }

  // 预加载主题
  const preloadTheme = async (bizCode) => {
    try {
      await themeManager.preloadTheme(bizCode)
    } catch (err) {
      console.error('主题预加载失败:', err)
    }
  }

  // 预加载所有主题
  const preloadAllThemes = async () => {
    try {
      await themeManager.preloadAllThemes()
    } catch (err) {
      console.error('主题预加载失败:', err)
    }
  }

  // 生命周期
  onMounted(() => {
    // 监听主题变更事件
    window.addEventListener('themeChange', handleThemeChange)
    
    // 获取当前主题
    currentTheme.value = getCurrentTheme()
  })

  onUnmounted(() => {
    // 移除事件监听
    window.removeEventListener('themeChange', handleThemeChange)
  })

  return {
    // 响应式数据
    currentTheme,
    isLoading,
    error,
    
    // 计算属性
    themeColor,
    themeName,
    isOrangeTheme,
    isRedTheme,
    
    // 方法
    switchTheme,
    getCurrentTheme,
    getAvailableThemes,
    preloadTheme,
    preloadAllThemes
  }
}

/**
 * 主题样式相关的组合式函数
 */
export function useThemeStyles() {
  const { themeColor, themeName } = useTheme()

  // 获取主题相关的CSS类名
  const getThemeClass = (baseClass = '') => {
    return computed(() => {
      const classes = [baseClass]
      if (themeName.value) {
        classes.push(`theme-${themeName.value}`)
      }
      return classes.filter(Boolean).join(' ')
    })
  }

  // 获取主题相关的内联样式
  const getThemeStyle = (additionalStyles = {}) => {
    return computed(() => {
      return {
        '--theme-color': themeColor.value,
        '--theme-name': themeName.value,
        ...additionalStyles
      }
    })
  }

  // 获取主题按钮样式
  const getThemeButtonClass = (type = 'primary') => {
    return computed(() => {
      return `btn-theme-${type}`
    })
  }

  // 获取主题文本颜色
  const getThemeTextStyle = () => {
    return computed(() => {
      return {
        color: themeColor.value
      }
    })
  }

  // 获取主题背景颜色
  const getThemeBackgroundStyle = (opacity = 1) => {
    return computed(() => {
      const color = themeColor.value
      if (opacity < 1) {
        // 转换为 rgba
        const hex = color.replace('#', '')
        const r = parseInt(hex.substr(0, 2), 16)
        const g = parseInt(hex.substr(2, 2), 16)
        const b = parseInt(hex.substr(4, 2), 16)
        return {
          backgroundColor: `rgba(${r}, ${g}, ${b}, ${opacity})`
        }
      }
      return {
        backgroundColor: color
      }
    })
  }

  // 获取主题边框样式
  const getThemeBorderStyle = (width = '1px', style = 'solid') => {
    return computed(() => {
      return {
        border: `${width} ${style} ${themeColor.value}`
      }
    })
  }

  return {
    getThemeClass,
    getThemeStyle,
    getThemeButtonClass,
    getThemeTextStyle,
    getThemeBackgroundStyle,
    getThemeBorderStyle
  }
}

/**
 * 主题配置相关的组合式函数
 */
export function useThemeConfig() {
  // 从 URL 获取 bizCode
  const getBizCodeFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('bizCode') || 'default'
  }

  // 设置 bizCode 到 URL
  const setBizCodeToUrl = (bizCode) => {
    const url = new URL(window.location)
    url.searchParams.set('bizCode', bizCode)
    window.history.replaceState({}, '', url)
  }

  // 从本地存储获取 bizCode
  const getBizCodeFromStorage = () => {
    return localStorage.getItem('bizCode') || 'default'
  }

  // 设置 bizCode 到本地存储
  const setBizCodeToStorage = (bizCode) => {
    localStorage.setItem('bizCode', bizCode)
  }

  // 获取主题配置
  const getThemeConfigByBizCode = (bizCode) => {
    return themeManager.getThemeConfig(bizCode)
  }

  return {
    getBizCodeFromUrl,
    setBizCodeToUrl,
    getBizCodeFromStorage,
    setBizCodeToStorage,
    getThemeConfigByBizCode
  }
}
