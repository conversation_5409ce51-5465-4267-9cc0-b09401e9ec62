// 京东主题变量
// 导入基础变量
@import 'variables-base.less';

// ======== 主题色系统 ========

// 主品牌色
@theme-color: #FF2F2F; // 京东红主色
@theme-color-light: #FF5555; // 浅红色
@theme-color-dark: #E62E2E; // 深红色

// 主题渐变色
@gradient-theme-106: linear-gradient(106deg, #FF5555 0%, #FF2F2F 100%); // 按钮渐变色 106度
@gradient-theme-115: linear-gradient(115deg, #FF5555 0%, #FF2F2F 100%); // 搜索按钮渐变色 115度
@gradient-theme-dark: linear-gradient(106deg, darken(#FF5555, 10%) 0%, darken(#FF2F2F, 10%) 100%); // 按钮激活状态渐变色

// 主题相关背景色
@bg-color-theme-light: #fff0f0; // 主题浅色背景
@bg-color-theme-lighter: #fff7f7; // 主题更浅背景

// 主题相关边框色
@border-color-theme: #ffd1d1; // 主题边框色
@border-color-theme-light: #ffd9d9; // 主题浅色边框

// 主题相关文字色
@text-color-theme: #FF2F2F; // 主题色文字
@text-color-theme-dark: #E62E2E; // 主题深色文字

// ======== 主题按钮变量 ========

// 主按钮 - 使用主题色
@btn-primary-bg: @theme-color;
@btn-primary-bg-hover: darken(@theme-color, 5%);
@btn-primary-bg-active: darken(@theme-color, 10%);
@btn-primary-color: @color-white;
@btn-primary-border: @theme-color;

// 次按钮 - 主题色边框
@btn-secondary-bg: @bg-color-white;
@btn-secondary-bg-hover: @bg-color-theme-lighter;
@btn-secondary-bg-active: @bg-color-theme-light;
@btn-secondary-color: @theme-color;
@btn-secondary-border: @theme-color;

// 文字按钮
@btn-text-color: @theme-color;
@btn-text-color-hover: darken(@theme-color, 10%);

// 链接色
@link-color: @theme-color;
@link-color-hover: darken(@theme-color, 10%);
@link-color-active: darken(@theme-color, 15%);

// ======== 主题状态色 ========

// 成功状态（保持绿色）
@color-success: @color-green;
@color-success-light: lighten(@color-green, 40%);
@color-success-dark: darken(@color-green, 10%);

// 警告状态（保持黄色）
@color-warning: @color-yellow;
@color-warning-light: lighten(@color-yellow, 35%);
@color-warning-dark: darken(@color-yellow, 10%);

// 错误状态（使用主题红色系）
@color-error: @theme-color;
@color-error-light: @bg-color-theme-light;
@color-error-dark: darken(@theme-color, 10%);

// 信息状态（使用蓝色）
@color-info: @color-blue;
@color-info-light: lighten(@color-blue, 40%);
@color-info-dark: darken(@color-blue, 10%);
