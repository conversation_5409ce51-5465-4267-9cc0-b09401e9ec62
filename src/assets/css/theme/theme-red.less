// ======== 红色主题 (京东红) ========
// 主题标识: red
// 主色调: #FF2F2F

// 导入基础变量和mixins
@import './variables.less';
@import './mixins.less';

// ======== 主题色定义 ========
@theme-color: #FF2F2F; // 京东红主色
@theme-color-light: lighten(@theme-color, 10%); // 浅色主题色
@theme-color-dark: darken(@theme-color, 10%); // 深色主题色
@theme-color-lighter: lighten(@theme-color, 20%); // 更浅主题色
@theme-color-darker: darken(@theme-color, 20%); // 更深主题色

// ======== 主题渐变色 ========
@theme-gradient-1: linear-gradient(106deg, #FF5555 0%, #FF2F2F 100%); // 按钮渐变色 106度
@theme-gradient-2: linear-gradient(115deg, #FF5555 0%, #FF2F2F 100%); // 搜索按钮渐变色 115度
@theme-gradient-3: linear-gradient(106deg, darken(#FF5555, 10%) 0%, darken(#FF2F2F, 10%) 100%); // 按钮激活状态渐变色
@theme-gradient-4: linear-gradient(135deg, @theme-color 0%, @theme-color-dark 100%); // 卡片渐变背景
@theme-gradient-5: linear-gradient(90deg, fade(@theme-color, 10%) 0%, fade(@theme-color, 30%) 100%); // 轻微渐变背景

// ======== 主题相关颜色 ========
@theme-bg-1: #fff5f5; // 主题背景色-1 (浅红背景)
@theme-bg-2: #fecaca; // 主题背景色-2 (中红背景)
@theme-bg-3: fade(@theme-color, 10%); // 主题背景色-3 (10%透明度)
@theme-bg-4: fade(@theme-color, 20%); // 主题背景色-4 (20%透明度)
@theme-bg-5: fade(@theme-color, 5%); // 主题背景色-5 (5%透明度)

@theme-border-1: #fecaca; // 主题边框色-1
@theme-border-2: fade(@theme-color, 30%); // 主题边框色-2
@theme-border-3: fade(@theme-color, 50%); // 主题边框色-3

@theme-text-1: @theme-color; // 主题文字色-1
@theme-text-2: @theme-color-dark; // 主题文字色-2
@theme-text-3: @theme-color-light; // 主题文字色-3

// ======== 状态颜色 ========
@theme-hover: fade(@theme-color, 80%); // 悬停状态
@theme-active: @theme-color-dark; // 激活状态
@theme-focus: fade(@theme-color, 20%); // 聚焦状态
@theme-disabled: fade(@theme-color, 30%); // 禁用状态

// ======== 主题按钮样式 ========
.btn-theme-primary {
  .button-primary(@theme-color, @color-white);
  .button-size-medium();
  
  &:hover:not(:disabled) {
    background-color: @theme-color-light;
  }
  
  &:active:not(:disabled) {
    background-color: @theme-color-dark;
  }
}

.btn-theme-secondary {
  .button-secondary(@theme-color, @theme-color);
  .button-size-medium();
}

.btn-theme-text {
  .button-text(@theme-color);
  .button-size-medium();
}

.btn-theme-gradient {
  .button-base();
  .button-size-medium();
  background: @theme-gradient-1;
  color: @color-white;
  border-radius: @radius-9999;
  
  &:hover:not(:disabled) {
    background: @theme-gradient-3;
  }
}

// ======== 主题输入框样式 ========
.input-theme {
  .input-base();
  
  &:focus {
    border-color: @theme-color;
    box-shadow: 0 0 0 2px @theme-focus;
  }
}

// ======== 主题卡片样式 ========
.card-theme {
  .card-base();
  border-top: 3px solid @theme-color;
}

.card-theme-gradient {
  .card-base();
  background: @theme-gradient-5;
  border: 1px solid @theme-border-1;
}

// ======== 主题标签样式 ========
.tag-theme-primary {
  display: inline-block;
  padding: @padding-4 @padding-8;
  font-size: @font-size-12;
  color: @color-white;
  background-color: @theme-color;
  border-radius: @radius-4;
}

.tag-theme-light {
  display: inline-block;
  padding: @padding-4 @padding-8;
  font-size: @font-size-12;
  color: @theme-color;
  background-color: @theme-bg-1;
  border: 1px solid @theme-border-1;
  border-radius: @radius-4;
}

// ======== 主题进度条样式 ========
.progress-theme {
  width: 100%;
  height: @height-20;
  background-color: @color-gray-200;
  border-radius: @radius-10;
  overflow: hidden;
  
  .progress-bar {
    height: 100%;
    background: @theme-gradient-1;
    border-radius: @radius-10;
    transition: width @transition-base ease;
  }
}

// ======== 主题开关样式 ========
.switch-theme {
  position: relative;
  display: inline-block;
  width: @width-44;
  height: @height-24;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: @color-gray-300;
    transition: @transition-base;
    border-radius: @radius-9999;
    
    &:before {
      position: absolute;
      content: "";
      height: @height-20;
      width: @width-20;
      left: 2px;
      bottom: 2px;
      background-color: @color-white;
      transition: @transition-base;
      border-radius: 50%;
    }
  }
  
  input:checked + .slider {
    background-color: @theme-color;
  }
  
  input:checked + .slider:before {
    transform: translateX(@width-20);
  }
}

// ======== 主题链接样式 ========
.link-theme {
  color: @theme-color;
  text-decoration: none;
  transition: color @transition-base ease;
  
  &:hover {
    color: @theme-color-dark;
    text-decoration: underline;
  }
  
  &:active {
    color: @theme-color-darker;
  }
}

// ======== 主题分割线样式 ========
.divider-theme {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, @theme-color 50%, transparent 100%);
  border: none;
  margin: @margin-16 0;
}

// ======== 主题提示框样式 ========
.alert-theme {
  padding: @padding-12 @padding-16;
  background-color: @theme-bg-1;
  border: 1px solid @theme-border-1;
  border-left: 4px solid @theme-color;
  border-radius: @radius-6;
  color: @theme-color-dark;
  
  .alert-title {
    font-weight: @font-weight-600;
    margin-bottom: @margin-4;
  }
}

// ======== 主题徽章样式 ========
.badge-theme {
  display: inline-block;
  min-width: @width-20;
  height: @height-20;
  padding: 0 @padding-6;
  font-size: @font-size-11;
  font-weight: @font-weight-500;
  line-height: @height-20;
  color: @color-white;
  text-align: center;
  background-color: @theme-color;
  border-radius: @radius-10;
}

// ======== 主题工具类 ========
.text-theme {
  color: @theme-color !important;
}

.bg-theme {
  background-color: @theme-color !important;
}

.border-theme {
  border-color: @theme-color !important;
}

.bg-theme-light {
  background-color: @theme-bg-1 !important;
}

.border-theme-light {
  border-color: @theme-border-1 !important;
}
