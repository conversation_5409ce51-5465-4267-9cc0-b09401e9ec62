// ======== 基础变量系统 ========
// 所有变量使用 xxx-数字 命名规范，便于区分和使用

// ======== 一、字号变量 ========
@font-size-10: 10px;
@font-size-11: 11px;
@font-size-12: 12px;
@font-size-13: 13px;
@font-size-14: 14px;
@font-size-15: 15px;
@font-size-16: 16px;
@font-size-17: 17px;
@font-size-18: 18px;
@font-size-19: 19px;
@font-size-20: 20px;
@font-size-21: 21px;
@font-size-22: 22px;
@font-size-24: 24px;
@font-size-26: 26px;
@font-size-28: 28px;
@font-size-30: 30px;
@font-size-32: 32px;
@font-size-36: 36px;
@font-size-40: 40px;

// ======== 二、字重变量 ========
@font-weight-100: 100;
@font-weight-200: 200;
@font-weight-300: 300;
@font-weight-400: 400;
@font-weight-500: 500;
@font-weight-600: 600;
@font-weight-700: 700;
@font-weight-800: 800;
@font-weight-900: 900;

// ======== 三、行高变量 ========
@line-height-12: 12px;
@line-height-14: 14px;
@line-height-16: 16px;
@line-height-18: 18px;
@line-height-20: 20px;
@line-height-22: 22px;
@line-height-24: 24px;
@line-height-26: 26px;
@line-height-28: 28px;
@line-height-30: 30px;
@line-height-32: 32px;
@line-height-36: 36px;
@line-height-40: 40px;
@line-height-44: 44px;
@line-height-48: 48px;

// ======== 四、内边距变量 (padding) ========
@padding-2: 2px;
@padding-4: 4px;
@padding-6: 6px;
@padding-8: 8px;
@padding-10: 10px;
@padding-12: 12px;
@padding-14: 14px;
@padding-16: 16px;
@padding-18: 18px;
@padding-20: 20px;

// ======== 五、外边距变量 (margin) ========
@margin-2: 2px;
@margin-4: 4px;
@margin-6: 6px;
@margin-8: 8px;
@margin-10: 10px;
@margin-12: 12px;
@margin-14: 14px;
@margin-16: 16px;
@margin-18: 18px;
@margin-20: 20px;

// ======== 六、圆角变量 ========
@radius-2: 2px;
@radius-4: 4px;
@radius-6: 6px;
@radius-8: 8px;
@radius-10: 10px;
@radius-12: 12px;
@radius-14: 14px;
@radius-16: 16px;
@radius-18: 18px;
@radius-20: 20px;
@radius-22: 22px;
@radius-24: 24px;
@radius-26: 26px;
@radius-28: 28px;
@radius-30: 30px;
@radius-50: 50px;
@radius-9999: 9999px;

// ======== 七、宽度变量 ========
@width-20: 20px;
@width-24: 24px;
@width-28: 28px;
@width-32: 32px;
@width-36: 36px;
@width-40: 40px;
@width-44: 44px;
@width-48: 48px;
@width-52: 52px;
@width-56: 56px;
@width-60: 60px;
@width-64: 64px;
@width-68: 68px;
@width-72: 72px;
@width-76: 76px;
@width-80: 80px;
@width-90: 90px;
@width-100: 100px;
@width-120: 120px;
@width-140: 140px;
@width-160: 160px;
@width-180: 180px;
@width-200: 200px;

// ======== 八、高度变量 ========
@height-20: 20px;
@height-24: 24px;
@height-28: 28px;
@height-32: 32px;
@height-36: 36px;
@height-40: 40px;
@height-44: 44px;
@height-48: 48px;
@height-52: 52px;
@height-56: 56px;
@height-60: 60px;
@height-64: 64px;
@height-68: 68px;
@height-72: 72px;
@height-76: 76px;
@height-80: 80px;

// ======== 九、透明度变量 ========
@opacity-10: 0.1;
@opacity-20: 0.2;
@opacity-30: 0.3;
@opacity-40: 0.4;
@opacity-50: 0.5;
@opacity-60: 0.6;
@opacity-70: 0.7;
@opacity-80: 0.8;
@opacity-90: 0.9;
@opacity-100: 1;

// ======== 十、阴影变量 ========
@shadow-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
@shadow-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
@shadow-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
@shadow-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
@shadow-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

// ======== 十一、z-index变量 ========
@z-index-1: 1;
@z-index-10: 10;
@z-index-50: 50;
@z-index-100: 100;
@z-index-500: 500;
@z-index-1000: 1000;
@z-index-1500: 1500;
@z-index-2000: 2000;
@z-index-9999: 9999;

// ======== 十二、字体家族变量 ========
@font-family-base: D-DIN-PRO-SemiBold, OPPOSans-R, PingFangSC-Regular, sans-serif;
@font-family-mono: 'Courier New', Courier, monospace;
@font-family-serif: Georgia, 'Times New Roman', Times, serif;

// ======== 十三、过渡动画变量 ========
@transition-fast: 0.15s;
@transition-base: 0.3s;
@transition-slow: 0.5s;
@transition-slower: 0.8s;

// ======== 十四、边框变量 ========
@border-width-1: 1px;
@border-width-2: 2px;
@border-width-3: 3px;
@border-width-4: 4px;
@border-width-5: 5px;

// ======== 十五、基础颜色变量 ========
@color-white: #FFFFFF;
@color-black: #000000;
@color-transparent: transparent;

// 灰色系
@color-gray-50: #F9FAFB;
@color-gray-100: #F3F4F6;
@color-gray-200: #E5E7EB;
@color-gray-300: #D1D5DB;
@color-gray-400: #9CA3AF;
@color-gray-500: #6B7280;
@color-gray-600: #4B5563;
@color-gray-700: #374151;
@color-gray-800: #1F2937;
@color-gray-900: #111827;

// 功能色
@color-success: #00C35A;
@color-warning: #FFA300;
@color-error: #FF2F2F;
@color-info: #1890FF;

// 中性色 - 文字
@text-color-primary: #171E24;
@text-color-secondary: #5A6066;
@text-color-tertiary: #879099;
@text-color-disabled: #CED0D8;
@text-color-white: #FFFFFF;
@text-color-tips: #FF4141;

// 中性色 - 背景
@bg-color-gray: #F8F9FA;
@bg-color-white: #FFFFFF;
@bg-color-tips: #fff7f0;
@border-color-tips: #ffe4d1;

// 分割线
@divider-color-base: #E2E8EE;

// 遮罩
@mask-color-065: rgba(0, 0, 0, 0.65);
@mask-color-05: rgba(255, 255, 255, 0.5);
