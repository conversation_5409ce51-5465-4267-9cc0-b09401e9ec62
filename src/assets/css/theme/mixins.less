// ======== 复用 Mixins 系统 ========

// ======== 一、文本相关 Mixins ========

// 文本溢出省略号
.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本溢出省略号
.multi-ellipsis(@lines) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}

// 文本居中
.text-center() {
  text-align: center;
}

// 文本左对齐
.text-left() {
  text-align: left;
}

// 文本右对齐
.text-right() {
  text-align: right;
}

// 文本不换行
.text-nowrap() {
  white-space: nowrap;
}

// 文本换行
.text-wrap() {
  white-space: normal;
  word-wrap: break-word;
}

// ======== 二、布局相关 Mixins ========

// Flex 布局基础
.flex() {
  display: flex;
}

// Flex 居中
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flex 水平居中
.flex-center-x() {
  display: flex;
  justify-content: center;
}

// Flex 垂直居中
.flex-center-y() {
  display: flex;
  align-items: center;
}

// Flex 两端对齐
.flex-between() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// Flex 环绕对齐
.flex-around() {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

// Flex 列布局
.flex-column() {
  display: flex;
  flex-direction: column;
}

// Flex 列居中
.flex-column-center() {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// 绝对定位居中
.absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 绝对定位水平居中
.absolute-center-x() {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

// 绝对定位垂直居中
.absolute-center-y() {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// ======== 三、按钮相关 Mixins ========

// 按钮基础样式
.button-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all @transition-base ease;
  user-select: none;
  outline: none;
  text-decoration: none;
  
  &:disabled {
    cursor: not-allowed;
    opacity: @opacity-50;
  }
}

// 主按钮样式
.button-primary(@bg-color: @theme-color, @text-color: @color-white) {
  .button-base();
  background-color: @bg-color;
  color: @text-color;
  border-radius: @radius-9999;
  
  &:hover:not(:disabled) {
    opacity: @opacity-90;
  }
  
  &:active:not(:disabled) {
    opacity: @opacity-80;
  }
}

// 次要按钮样式
.button-secondary(@border-color: @theme-color, @text-color: @theme-color) {
  .button-base();
  background-color: @color-white;
  color: @text-color;
  border: @border-width-1 solid @border-color;
  border-radius: @radius-9999;
  
  &:hover:not(:disabled) {
    background-color: @border-color;
    color: @color-white;
  }
}

// 文本按钮样式
.button-text(@text-color: @theme-color) {
  .button-base();
  background-color: transparent;
  color: @text-color;
  border: none;
  
  &:hover:not(:disabled) {
    opacity: @opacity-70;
  }
}

// 按钮尺寸 - 大
.button-size-large() {
  height: @height-48;
  padding: 0 @padding-20;
  font-size: @font-size-16;
  font-weight: @font-weight-500;
}

// 按钮尺寸 - 中
.button-size-medium() {
  height: @height-40;
  padding: 0 @padding-16;
  font-size: @font-size-14;
  font-weight: @font-weight-500;
}

// 按钮尺寸 - 小
.button-size-small() {
  height: @height-32;
  padding: 0 @padding-12;
  font-size: @font-size-12;
  font-weight: @font-weight-400;
}

// 按钮尺寸 - 迷你
.button-size-mini() {
  height: @height-24;
  padding: 0 @padding-8;
  font-size: @font-size-11;
  font-weight: @font-weight-400;
}

// ======== 四、表单相关 Mixins ========

// 输入框基础样式
.input-base() {
  display: block;
  width: 100%;
  padding: @padding-12 @padding-16;
  font-size: @font-size-14;
  line-height: @line-height-20;
  color: @text-color-primary;
  background-color: @color-white;
  border: @border-width-1 solid @color-gray-300;
  border-radius: @radius-6;
  transition: border-color @transition-base ease;
  outline: none;
  
  &:focus {
    border-color: @theme-color;
    box-shadow: 0 0 0 2px fade(@theme-color, 20%);
  }
  
  &:disabled {
    background-color: @color-gray-100;
    color: @text-color-disabled;
    cursor: not-allowed;
  }
  
  &::placeholder {
    color: @text-color-tertiary;
  }
}

// ======== 五、卡片相关 Mixins ========

// 卡片基础样式
.card-base() {
  background-color: @color-white;
  border-radius: @radius-8;
  box-shadow: @shadow-1;
  overflow: hidden;
}

// 卡片内容区域
.card-body(@padding: @padding-16) {
  padding: @padding;
}

// 卡片头部
.card-header(@padding: @padding-16) {
  padding: @padding;
  border-bottom: @border-width-1 solid @color-gray-200;
}

// 卡片底部
.card-footer(@padding: @padding-16) {
  padding: @padding;
  border-top: @border-width-1 solid @color-gray-200;
}

// ======== 六、滚动条相关 Mixins ========

// 隐藏滚动条
.no-scrollbar() {
  &::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }
  
  scrollbar-width: none;
  -ms-overflow-style: none;
}

// 自定义滚动条
.custom-scrollbar(@width: 6px, @track-color: @color-gray-100, @thumb-color: @color-gray-400) {
  &::-webkit-scrollbar {
    width: @width;
    height: @width;
  }
  
  &::-webkit-scrollbar-track {
    background: @track-color;
    border-radius: @width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: @thumb-color;
    border-radius: @width / 2;
    
    &:hover {
      background: darken(@thumb-color, 10%);
    }
  }
}

// ======== 七、动画相关 Mixins ========

// 淡入动画
.fade-in(@duration: @transition-base) {
  animation: fadeIn @duration ease-in-out;
}

// 淡出动画
.fade-out(@duration: @transition-base) {
  animation: fadeOut @duration ease-in-out;
}

// 滑入动画
.slide-in-up(@duration: @transition-base) {
  animation: slideInUp @duration ease-out;
}

// 缩放动画
.scale-in(@duration: @transition-base) {
  animation: scaleIn @duration ease-out;
}

// ======== 八、响应式相关 Mixins ========

// 移动端优先
.mobile-first(@rules) {
  @rules();
}

// 平板及以上
.tablet-up(@rules) {
  @media (min-width: 768px) {
    @rules();
  }
}

// 桌面及以上
.desktop-up(@rules) {
  @media (min-width: 1024px) {
    @rules();
  }
}

// 大屏及以上
.large-up(@rules) {
  @media (min-width: 1280px) {
    @rules();
  }
}
