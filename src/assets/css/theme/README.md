# 主题系统使用指南

## 概述

本项目实现了一套完整的主题系统，支持根据 `bizCode` 动态加载不同的主题文件。目前支持两套主题：

- **橙色主题** (`#FF7A0A`) - 沃橙色/品牌橙主色
- **红色主题** (`#FF2F2F`) - 京东红主色

## 文件结构

```
src/assets/css/theme/
├── variables.less          # 基础变量文件
├── mixins.less             # 复用 mixins
├── theme-orange.less       # 橙色主题
├── theme-red.less          # 红色主题
├── index.less              # 主题系统入口
└── README.md               # 使用文档

src/utils/
└── themeManager.js         # 主题管理器

src/composables/
└── useTheme.js             # Vue 组合式函数

src/plugins/
└── vite-theme-plugin.js    # Vite 主题插件

src/components/
└── ThemeDemo.vue           # 主题演示组件
```

## 快速开始

### 1. 基础使用

在 Vue 组件中使用主题：

```vue
<template>
  <div>
    <!-- 使用主题按钮 -->
    <button class="btn-theme-primary">主要按钮</button>
    <button class="btn-theme-secondary">次要按钮</button>
    
    <!-- 使用主题输入框 -->
    <input type="text" class="input-theme" placeholder="请输入内容" />
    
    <!-- 使用主题卡片 -->
    <div class="card-theme">
      <div class="card-body">卡片内容</div>
    </div>
  </div>
</template>

<script setup>
import { useTheme } from '@/composables/useTheme.js'

const { themeColor, themeName, switchTheme } = useTheme()

// 切换主题
const handleThemeSwitch = () => {
  switchTheme('jd') // 切换到京东红色主题
}
</script>
```

### 2. 使用组合式函数

```javascript
import { useTheme, useThemeStyles } from '@/composables/useTheme.js'

// 基础主题功能
const {
  currentTheme,      // 当前主题信息
  themeColor,        // 主题颜色
  themeName,         // 主题名称
  isOrangeTheme,     // 是否为橙色主题
  isRedTheme,        // 是否为红色主题
  switchTheme,       // 切换主题
  preloadTheme       // 预加载主题
} = useTheme()

// 主题样式功能
const {
  getThemeClass,           // 获取主题类名
  getThemeStyle,           // 获取主题样式
  getThemeButtonClass,     // 获取主题按钮类名
  getThemeTextStyle,       // 获取主题文本样式
  getThemeBackgroundStyle, // 获取主题背景样式
  getThemeBorderStyle      // 获取主题边框样式
} = useThemeStyles()
```

## 变量系统

### 字号变量 (xxx-数字命名)

```less
@font-size-10: 10px;
@font-size-11: 11px;
@font-size-12: 12px;
// ... 到 @font-size-40
```

### 字重变量

```less
@font-weight-100: 100;
@font-weight-200: 200;
// ... 到 @font-weight-900
```

### 间距变量 (2px - 20px)

```less
// 内边距
@padding-2: 2px;
@padding-4: 4px;
// ... 到 @padding-20

// 外边距
@margin-2: 2px;
@margin-4: 4px;
// ... 到 @margin-20
```

### 圆角变量

```less
@radius-2: 2px;
@radius-4: 4px;
// ... 到 @radius-9999
```

## 工具类

### 间距工具类

```html
<!-- 内边距 -->
<div class="p-2">padding: 2px</div>
<div class="p-8">padding: 8px</div>
<div class="pt-4">padding-top: 4px</div>
<div class="px-12">padding-left/right: 12px</div>
<div class="py-16">padding-top/bottom: 16px</div>

<!-- 外边距 -->
<div class="m-4">margin: 4px</div>
<div class="mt-8">margin-top: 8px</div>
<div class="mx-12">margin-left/right: 12px</div>
<div class="my-16">margin-top/bottom: 16px</div>
```

### 字体工具类

```html
<div class="font-12">字号 12px</div>
<div class="font-16">字号 16px</div>
<div class="font-400">字重 400</div>
<div class="font-600">字重 600</div>
```

### 主题工具类

```html
<div class="text-theme">主题色文字</div>
<div class="bg-theme">主题色背景</div>
<div class="border-theme">主题色边框</div>
<div class="bg-theme-light">浅色主题背景</div>
```

### 布局工具类

```html
<div class="flex">弹性布局</div>
<div class="flex-center">居中布局</div>
<div class="flex-between">两端对齐</div>
<div class="flex-column">列布局</div>
```

## 主题组件

### 按钮组件

```html
<button class="btn-theme-primary">主要按钮</button>
<button class="btn-theme-secondary">次要按钮</button>
<button class="btn-theme-text">文本按钮</button>
<button class="btn-theme-gradient">渐变按钮</button>
```

### 输入框组件

```html
<input type="text" class="input-theme" placeholder="主题输入框" />
```

### 卡片组件

```html
<div class="card-theme">
  <div class="card-body">普通主题卡片</div>
</div>

<div class="card-theme-gradient">
  <div class="card-body">渐变主题卡片</div>
</div>
```

### 标签组件

```html
<span class="tag-theme-primary">主要标签</span>
<span class="tag-theme-light">浅色标签</span>
<span class="badge-theme">徽章</span>
```

## 主题配置

### bizCode 映射

```javascript
const BIZ_CODE_THEME_MAP = {
  'unicom': 'orange',     // 联通 -> 橙色主题
  'default': 'orange',    // 默认 -> 橙色主题
  'jd': 'red',           // 京东 -> 红色主题
  'jingdong': 'red'      // 京东 -> 红色主题
}
```

### URL 参数

通过 URL 参数设置主题：

```
https://example.com?bizCode=jd        # 使用京东红色主题
https://example.com?bizCode=unicom    # 使用联通橙色主题
```

### 程序化切换

```javascript
import themeManager from '@/utils/themeManager.js'

// 切换到京东主题
themeManager.switchTheme('jd')

// 预加载主题
themeManager.preloadTheme('jd')

// 获取当前主题
const currentTheme = themeManager.getCurrentTheme()
```

## Mixins 使用

### 文本相关

```less
.my-text {
  .ellipsis();              // 单行省略号
  .multi-ellipsis(2);       // 多行省略号
  .text-center();           // 文本居中
}
```

### 布局相关

```less
.my-container {
  .flex-center();           // 弹性居中
  .flex-between();          // 两端对齐
  .absolute-center();       // 绝对定位居中
}
```

### 按钮相关

```less
.my-button {
  .button-primary();        // 主按钮样式
  .button-size-large();     // 大尺寸按钮
}
```

## 自定义主题

### 1. 创建新主题文件

```less
// src/assets/css/theme/theme-custom.less
@import './variables.less';
@import './mixins.less';

@theme-color: #your-color;
// ... 其他主题变量
```

### 2. 更新主题配置

```javascript
// src/utils/themeManager.js
const THEME_CONFIG = {
  'custom': {
    name: 'custom',
    file: 'theme-custom.less',
    color: '#your-color',
    description: '自定义主题'
  }
}
```

## 最佳实践

1. **使用变量而非硬编码值**
   ```less
   // ✅ 推荐
   .my-class {
     padding: @padding-16;
     font-size: @font-size-14;
   }
   
   // ❌ 不推荐
   .my-class {
     padding: 16px;
     font-size: 14px;
   }
   ```

2. **使用工具类减少重复代码**
   ```html
   <!-- ✅ 推荐 -->
   <div class="flex-center p-16 radius-8">内容</div>
   
   <!-- ❌ 不推荐 -->
   <div style="display: flex; align-items: center; justify-content: center; padding: 16px; border-radius: 8px;">内容</div>
   ```

3. **预加载主题提升用户体验**
   ```javascript
   // 在应用启动时预加载所有主题
   onMounted(() => {
     themeManager.preloadAllThemes()
   })
   ```

4. **监听主题变更事件**
   ```javascript
   window.addEventListener('themeChange', (event) => {
     console.log('主题已切换:', event.detail.theme)
   })
   ```

## 注意事项

1. 主题文件会动态加载，首次切换可能有短暂延迟
2. 建议在应用启动时预加载常用主题
3. 主题变量遵循 `xxx-数字` 命名规范，便于记忆和使用
4. 所有主题组件都支持响应式设计
5. 在生产环境中，主题文件会被自动压缩和优化
