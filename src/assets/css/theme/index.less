// ======== 主题系统入口文件 ========

// 导入基础变量和mixins（所有主题共用）
@import './variables.less';
@import './mixins.less';

// ======== 默认主题 ========
// 默认使用橙色主题
@import './theme-red.less';

// ======== 通用样式类 ========

// 字体样式类
.text-primary {
  color: @text-color-primary;
}

.text-secondary {
  color: @text-color-secondary;
}

.text-tertiary {
  color: @text-color-tertiary;
}

.text-disabled {
  color: @text-color-disabled;
}

.text-white {
  color: @color-white;
}

// 字号样式类
.font-10 { font-size: @font-size-10; }
.font-11 { font-size: @font-size-11; }
.font-12 { font-size: @font-size-12; }
.font-13 { font-size: @font-size-13; }
.font-14 { font-size: @font-size-14; }
.font-15 { font-size: @font-size-15; }
.font-16 { font-size: @font-size-16; }
.font-17 { font-size: @font-size-17; }
.font-18 { font-size: @font-size-18; }
.font-19 { font-size: @font-size-19; }
.font-20 { font-size: @font-size-20; }
.font-22 { font-size: @font-size-22; }
.font-24 { font-size: @font-size-24; }

// 字重样式类
.font-100 { font-weight: @font-weight-100; }
.font-200 { font-weight: @font-weight-200; }
.font-300 { font-weight: @font-weight-300; }
.font-400 { font-weight: @font-weight-400; }
.font-500 { font-weight: @font-weight-500; }
.font-600 { font-weight: @font-weight-600; }
.font-700 { font-weight: @font-weight-700; }
.font-800 { font-weight: @font-weight-800; }
.font-900 { font-weight: @font-weight-900; }

// 内边距样式类
.p-2 { padding: @padding-2; }
.p-4 { padding: @padding-4; }
.p-6 { padding: @padding-6; }
.p-8 { padding: @padding-8; }
.p-10 { padding: @padding-10; }
.p-12 { padding: @padding-12; }
.p-14 { padding: @padding-14; }
.p-16 { padding: @padding-16; }
.p-18 { padding: @padding-18; }
.p-20 { padding: @padding-20; }

.pt-2 { padding-top: @padding-2; }
.pt-4 { padding-top: @padding-4; }
.pt-6 { padding-top: @padding-6; }
.pt-8 { padding-top: @padding-8; }
.pt-10 { padding-top: @padding-10; }
.pt-12 { padding-top: @padding-12; }
.pt-14 { padding-top: @padding-14; }
.pt-16 { padding-top: @padding-16; }
.pt-18 { padding-top: @padding-18; }
.pt-20 { padding-top: @padding-20; }

.pr-2 { padding-right: @padding-2; }
.pr-4 { padding-right: @padding-4; }
.pr-6 { padding-right: @padding-6; }
.pr-8 { padding-right: @padding-8; }
.pr-10 { padding-right: @padding-10; }
.pr-12 { padding-right: @padding-12; }
.pr-14 { padding-right: @padding-14; }
.pr-16 { padding-right: @padding-16; }
.pr-18 { padding-right: @padding-18; }
.pr-20 { padding-right: @padding-20; }

.pb-2 { padding-bottom: @padding-2; }
.pb-4 { padding-bottom: @padding-4; }
.pb-6 { padding-bottom: @padding-6; }
.pb-8 { padding-bottom: @padding-8; }
.pb-10 { padding-bottom: @padding-10; }
.pb-12 { padding-bottom: @padding-12; }
.pb-14 { padding-bottom: @padding-14; }
.pb-16 { padding-bottom: @padding-16; }
.pb-18 { padding-bottom: @padding-18; }
.pb-20 { padding-bottom: @padding-20; }

.pl-2 { padding-left: @padding-2; }
.pl-4 { padding-left: @padding-4; }
.pl-6 { padding-left: @padding-6; }
.pl-8 { padding-left: @padding-8; }
.pl-10 { padding-left: @padding-10; }
.pl-12 { padding-left: @padding-12; }
.pl-14 { padding-left: @padding-14; }
.pl-16 { padding-left: @padding-16; }
.pl-18 { padding-left: @padding-18; }
.pl-20 { padding-left: @padding-20; }

.px-2 { padding-left: @padding-2; padding-right: @padding-2; }
.px-4 { padding-left: @padding-4; padding-right: @padding-4; }
.px-6 { padding-left: @padding-6; padding-right: @padding-6; }
.px-8 { padding-left: @padding-8; padding-right: @padding-8; }
.px-10 { padding-left: @padding-10; padding-right: @padding-10; }
.px-12 { padding-left: @padding-12; padding-right: @padding-12; }
.px-14 { padding-left: @padding-14; padding-right: @padding-14; }
.px-16 { padding-left: @padding-16; padding-right: @padding-16; }
.px-18 { padding-left: @padding-18; padding-right: @padding-18; }
.px-20 { padding-left: @padding-20; padding-right: @padding-20; }

.py-2 { padding-top: @padding-2; padding-bottom: @padding-2; }
.py-4 { padding-top: @padding-4; padding-bottom: @padding-4; }
.py-6 { padding-top: @padding-6; padding-bottom: @padding-6; }
.py-8 { padding-top: @padding-8; padding-bottom: @padding-8; }
.py-10 { padding-top: @padding-10; padding-bottom: @padding-10; }
.py-12 { padding-top: @padding-12; padding-bottom: @padding-12; }
.py-14 { padding-top: @padding-14; padding-bottom: @padding-14; }
.py-16 { padding-top: @padding-16; padding-bottom: @padding-16; }
.py-18 { padding-top: @padding-18; padding-bottom: @padding-18; }
.py-20 { padding-top: @padding-20; padding-bottom: @padding-20; }

// 外边距样式类
.m-2 { margin: @margin-2; }
.m-4 { margin: @margin-4; }
.m-6 { margin: @margin-6; }
.m-8 { margin: @margin-8; }
.m-10 { margin: @margin-10; }
.m-12 { margin: @margin-12; }
.m-14 { margin: @margin-14; }
.m-16 { margin: @margin-16; }
.m-18 { margin: @margin-18; }
.m-20 { margin: @margin-20; }

.mt-2 { margin-top: @margin-2; }
.mt-4 { margin-top: @margin-4; }
.mt-6 { margin-top: @margin-6; }
.mt-8 { margin-top: @margin-8; }
.mt-10 { margin-top: @margin-10; }
.mt-12 { margin-top: @margin-12; }
.mt-14 { margin-top: @margin-14; }
.mt-16 { margin-top: @margin-16; }
.mt-18 { margin-top: @margin-18; }
.mt-20 { margin-top: @margin-20; }

.mr-2 { margin-right: @margin-2; }
.mr-4 { margin-right: @margin-4; }
.mr-6 { margin-right: @margin-6; }
.mr-8 { margin-right: @margin-8; }
.mr-10 { margin-right: @margin-10; }
.mr-12 { margin-right: @margin-12; }
.mr-14 { margin-right: @margin-14; }
.mr-16 { margin-right: @margin-16; }
.mr-18 { margin-right: @margin-18; }
.mr-20 { margin-right: @margin-20; }

.mb-2 { margin-bottom: @margin-2; }
.mb-4 { margin-bottom: @margin-4; }
.mb-6 { margin-bottom: @margin-6; }
.mb-8 { margin-bottom: @margin-8; }
.mb-10 { margin-bottom: @margin-10; }
.mb-12 { margin-bottom: @margin-12; }
.mb-14 { margin-bottom: @margin-14; }
.mb-16 { margin-bottom: @margin-16; }
.mb-18 { margin-bottom: @margin-18; }
.mb-20 { margin-bottom: @margin-20; }

.ml-2 { margin-left: @margin-2; }
.ml-4 { margin-left: @margin-4; }
.ml-6 { margin-left: @margin-6; }
.ml-8 { margin-left: @margin-8; }
.ml-10 { margin-left: @margin-10; }
.ml-12 { margin-left: @margin-12; }
.ml-14 { margin-left: @margin-14; }
.ml-16 { margin-left: @margin-16; }
.ml-18 { margin-left: @margin-18; }
.ml-20 { margin-left: @margin-20; }

.mx-2 { margin-left: @margin-2; margin-right: @margin-2; }
.mx-4 { margin-left: @margin-4; margin-right: @margin-4; }
.mx-6 { margin-left: @margin-6; margin-right: @margin-6; }
.mx-8 { margin-left: @margin-8; margin-right: @margin-8; }
.mx-10 { margin-left: @margin-10; margin-right: @margin-10; }
.mx-12 { margin-left: @margin-12; margin-right: @margin-12; }
.mx-14 { margin-left: @margin-14; margin-right: @margin-14; }
.mx-16 { margin-left: @margin-16; margin-right: @margin-16; }
.mx-18 { margin-left: @margin-18; margin-right: @margin-18; }
.mx-20 { margin-left: @margin-20; margin-right: @margin-20; }

.my-2 { margin-top: @margin-2; margin-bottom: @margin-2; }
.my-4 { margin-top: @margin-4; margin-bottom: @margin-4; }
.my-6 { margin-top: @margin-6; margin-bottom: @margin-6; }
.my-8 { margin-top: @margin-8; margin-bottom: @margin-8; }
.my-10 { margin-top: @margin-10; margin-bottom: @margin-10; }
.my-12 { margin-top: @margin-12; margin-bottom: @margin-12; }
.my-14 { margin-top: @margin-14; margin-bottom: @margin-14; }
.my-16 { margin-top: @margin-16; margin-bottom: @margin-16; }
.my-18 { margin-top: @margin-18; margin-bottom: @margin-18; }
.my-20 { margin-top: @margin-20; margin-bottom: @margin-20; }

// 圆角样式类
.radius-2 { border-radius: @radius-2; }
.radius-4 { border-radius: @radius-4; }
.radius-6 { border-radius: @radius-6; }
.radius-8 { border-radius: @radius-8; }
.radius-10 { border-radius: @radius-10; }
.radius-12 { border-radius: @radius-12; }
.radius-16 { border-radius: @radius-16; }
.radius-20 { border-radius: @radius-20; }
.radius-50 { border-radius: @radius-50; }
.radius-circle { border-radius: @radius-9999; }

// 布局相关样式类
.flex { .flex(); }
.flex-center { .flex-center(); }
.flex-center-x { .flex-center-x(); }
.flex-center-y { .flex-center-y(); }
.flex-between { .flex-between(); }
.flex-around { .flex-around(); }
.flex-column { .flex-column(); }
.flex-column-center { .flex-column-center(); }

// 文本相关样式类
.ellipsis { .ellipsis(); }
.text-center { .text-center(); }
.text-left { .text-left(); }
.text-right { .text-right(); }
.text-nowrap { .text-nowrap(); }

// 滚动条相关样式类
.no-scrollbar { .no-scrollbar(); }

// 全局基础样式
body {
  font-family: @font-family-base;
  font-size: @font-size-16;
  color: @text-color-primary;
  background-color: @bg-color-white;
  line-height: @line-height-20;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
