// 基础设计变量 - 与主题无关的通用变量
// 基于750设计稿（前端手动转化为375）

// ======== 字体系统 ========

// 字体家族
@font-family-base: D-DIN-PRO-SemiBold, OPPOSans-R, PingFangSC-Regular, sans-serif;

// 字号 - 使用数字命名便于记忆和使用
@font-size-11: 11px; // Tag标签文字、弱提示信息
@font-size-12: 12px; // Toast文案、辅助文字
@font-size-13: 13px; // 商品名称、地址、列表信息等
@font-size-14: 14px; // 输入框、可操作表单等
@font-size-15: 15px; // 输入框、可操作表单等
@font-size-16: 16px; // 名字电话地址等
@font-size-17: 17px; // 三级标题 弹窗、内容、卡片标题
@font-size-18: 18px; // 二级标题 模块标题
@font-size-20: 20px; // 一级标题 订单、支付状态
@font-size-22: 22px; // 最大标题

// 行高
@line-height-16: 16px;
@line-height-18: 18px;
@line-height-20: 20px;
@line-height-22: 22px;
@line-height-24: 24px;
@line-height-28: 28px;

// 字重
@font-weight-400: 400; // Regular
@font-weight-500: 500; // Medium
@font-weight-600: 600; // SemiBold
@font-weight-700: 700; // Bold

// ======== 间距系统 ========

// 外边距 margin
@margin-2: 2px;
@margin-4: 4px;
@margin-6: 6px;
@margin-8: 8px;
@margin-10: 10px;
@margin-12: 12px;
@margin-14: 14px;
@margin-16: 16px;
@margin-18: 18px;
@margin-20: 20px;

// 内边距 padding
@padding-2: 2px;
@padding-4: 4px;
@padding-6: 6px;
@padding-8: 8px;
@padding-10: 10px;
@padding-12: 12px;
@padding-14: 14px;
@padding-16: 16px;
@padding-18: 18px;
@padding-20: 20px;
@padding-page: 17px;

// ======== 圆角系统 ========
@radius-2: 2px; // 标签、小按钮
@radius-4: 4px; // 商品图片容器
@radius-6: 6px; // 瓷片区、大背景块
@radius-8: 8px; // 瓷片区、大背景块
@radius-10: 10px; // 模块
@radius-12: 12px; // 底部弹层、弹窗
@radius-15: 15px; // 小按钮圆角
@radius-18: 18px; // 中等按钮圆角
@radius-20: 20px; // 特殊按钮圆角
@radius-22: 22px; // 大按钮圆角
@radius-50: 50px; // 圆形按钮
@radius-9999: 9999px; // 完全圆角

// ======== 尺寸系统 ========

// 按钮高度
@button-height-24: 24px; // 最小按钮
@button-height-28: 28px; // 小按钮
@button-height-32: 32px; // 次按钮
@button-height-36: 36px; // 中等按钮
@button-height-38: 38px; // 次主按钮
@button-height-42: 42px; // 主按钮

// 按钮宽度
@button-width-80: 80px; // 小按钮宽度
@button-width-90: 90px; // 中等按钮宽度
@button-width-119: 119px; // 特殊按钮宽度
@button-width-160: 160px; // 大按钮宽度

// ======== 透明度系统 ========

@opacity-05: 0.5; // 50% 失效商品展示透明度
@opacity-065: 0.65; // 65% 页面蒙层透明度
@opacity-07: 0.7; // 70% 文本按钮激活透明度

// ======== 中性色系统 ========

// 基础颜色
@color-white: #FFFFFF;
@color-black: #000000;

// 文字色
@text-color-primary: #171E24; // 主要文字
@text-color-secondary: #5A6066; // 次重要文字
@text-color-tertiary: #879099; // 提示文字、辅助信息
@text-color-disabled: #CED0D8; // 弱提示、输入框引导、禁用
@text-color-white: #FFFFFF; // 白色文字
@text-color-placeholder: #CED0D8; // 输入框提示文字

// 背景色
@bg-color-white: #FFFFFF; // 主背景
@bg-color-gray: #F8F9FA; // 灰色背景
@bg-color-light: #FAFBFC; // 浅灰背景

// 边框色
@border-color-base: #E2E8EE; // 基础边框
@border-color-light: #F0F2F5; // 浅色边框
@border-color-dark: #D9DDE4; // 深色边框

// 分割线
@divider-color-base: #E2E8EE;

// 遮罩
@mask-color-065: rgba(0, 0, 0, 0.65);
@mask-color-05: rgba(255, 255, 255, 0.5);

// ======== 功能色系统 ========

@color-red: #FF2F2F; // 红色 - 错误、警告
@color-green: #00C35A; // 绿色 - 成功
@color-yellow: #FFA300; // 黄色 - 警告
@color-blue: #1890FF; // 蓝色 - 信息

// ======== 阴影系统 ========

@shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
@shadow-base: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
@shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
@shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);

// ======== Z-index 层级 ========

@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal-backdrop: 1040;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;